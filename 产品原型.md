<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>志愿AI规划师 - 小程序原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f7f8fa;
            color: #333;
        }
        
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        
        .page {
            display: none;
            height: 768px;
            overflow-y: auto;
        }
        
        .page.active {
            display: block;
        }
        
        /* 首页样式 */
        .home-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 20px;
            color: white;
            text-align: center;
        }
        
        .home-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .home-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .start-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .start-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .features {
            padding: 30px 20px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-right: 15px;
        }
        
        /* 信息收集页 */
        .form-container {
            padding: 20px;
        }
        
        .form-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .form-subtitle {
            color: #666;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .required {
            color: #e74c3c;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            background: white;
        }
        
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .checkbox-item {
            padding: 8px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 20px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .checkbox-item.selected {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .submit-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            margin-top: 20px;
            transition: transform 0.3s ease;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
        }
        
        /* 推荐结果页 */
        .result-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            color: white;
        }
        
        .result-title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .student-info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
        
        .recommendation-list {
            padding: 20px;
        }
        
        .recommendation-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .school-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .major-name {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .probability {
            display: inline-block;
            background: #f1f3f4;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .probability.high {
            background: #d4edda;
            color: #155724;
        }
        
        .probability.medium {
            background: #fff3cd;
            color: #856404;
        }
        
        .probability.low {
            background: #f8d7da;
            color: #721c24;
        }
        
        .preview-reason {
            color: #666;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .unlock-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 600;
        }
        
        /* 职业选择页 */
        .career-selection {
            padding: 20px;
        }
        
        .career-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .career-item:hover {
            transform: translateY(-2px);
        }
        
        .career-item.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .career-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .career-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .salary-range {
            color: #e74c3c;
            font-weight: 600;
        }
        
        .career-desc {
            color: #666;
            line-height: 1.5;
        }
        
        /* AI人生模拟页 */
        .simulation-container {
            padding: 20px;
        }
        
        .timeline {
            position: relative;
            margin: 20px 0;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #667eea;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            padding-left: 60px;
        }
        
        .timeline-dot {
            position: absolute;
            left: 11px;
            top: 0;
            width: 18px;
            height: 18px;
            background: #667eea;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .timeline-content {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .timeline-year {
            font-size: 14px;
            color: #667eea;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .timeline-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .timeline-desc {
            color: #666;
            line-height: 1.5;
        }
        
        /* 导航栏 */
        .nav-bar {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            background: white;
            border-top: 1px solid #e1e8ed;
            display: flex;
            padding: 10px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            cursor: pointer;
            padding: 5px;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .nav-text {
            font-size: 12px;
            color: #666;
        }
        
        .nav-item.active .nav-text {
            color: #667eea;
        }
        
        .progress-bar {
            height: 4px;
            background: #e1e8ed;
            border-radius: 2px;
            margin: 10px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .simulate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            margin-top: 20px;
            transition: transform 0.3s ease;
        }
        
        .simulate-btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">志愿AI规划师</div>
        
        <!-- 首页 -->
        <div class="page active" id="home">
            <div class="home-header">
                <h1 class="home-title">AI志愿规划师</h1>
                <p class="home-subtitle">不只告诉你选什么，更告诉你为什么选</p>
                <button class="start-btn" onclick="showPage('form')">开始规划人生</button>
            </div>
            
            <div class="features">
                <div class="feature-item">
                    <div class="feature-icon">🎯</div>
                    <div>
                        <h3>精准推荐</h3>
                        <p>基于分数和兴趣的个性化院校推荐</p>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">💡</div>
                    <div>
                        <h3>详细解释</h3>
                        <p>每个推荐都有专业理由，让你心里有底</p>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">🚀</div>
                    <div>
                        <h3>职业规划</h3>
                        <p>从专业选择到职业发展的全链条规划</p>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">🔮</div>
                    <div>
                        <h3>人生模拟</h3>
                        <p>AI预测你的职业发展轨迹和人生路径</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 信息收集页 -->
        <div class="page" id="form">
            <div class="form-container">
                <h2 class="form-title">告诉我你的情况</h2>
                <p class="form-subtitle">我需要了解你的基本信息来为你提供精准推荐</p>
                
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 30%"></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">高考分数 <span class="required">*</span></label>
                    <input type="number" class="form-input" placeholder="请输入你的高考分数" id="score">
                </div>
                
                <div class="form-group">
                    <label class="form-label">所在省份 <span class="required">*</span></label>
                    <select class="form-select" id="province">
                        <option value="">请选择省份</option>
                        <option value="hubei">湖北</option>
                        <option value="henan">河南</option>
                        <option value="shandong">山东</option>
                        <option value="guangdong">广东</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">选科组合 <span class="required">*</span></label>
                    <select class="form-select" id="subjects">
                        <option value="">请选择选科组合</option>
                        <option value="physics">物理+化学+生物</option>
                        <option value="physics2">物理+化学+地理</option>
                        <option value="history">历史+政治+地理</option>
                        <option value="history2">历史+政治+生物</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">意向地区</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item" onclick="toggleSelection(this)">不限</div>
                        <div class="checkbox-item" onclick="toggleSelection(this)">本省</div>
                        <div class="checkbox-item" onclick="toggleSelection(this)">北上广深</div>
                        <div class="checkbox-item" onclick="toggleSelection(this)">新一线城市</div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">专业倾向</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item" onclick="toggleSelection(this)">理工类</div>
                        <div class="checkbox-item" onclick="toggleSelection(this)">经管类</div>
                        <div class="checkbox-item" onclick="toggleSelection(this)">文史类</div>
                        <div class="checkbox-item" onclick="toggleSelection(this)">医学类</div>
                        <div class="checkbox-item" onclick="toggleSelection(this)">艺术类</div>
                    </div>
                </div>
                
                <button class="submit-btn" onclick="showPage('results')">生成推荐方案</button>
            </div>
        </div>
        
        <!-- 推荐结果页 -->
        <div class="page" id="results">
            <div class="result-header">
                <h2 class="result-title">为你推荐以下院校</h2>
                <div class="student-info">
                    <p>📊 分数：612分 (理科) | 📍 湖北 | 🎯 物理+化学+生物</p>
                    <p>🎖️ 预估位次：全省前5.2% | 💡 推荐策略：冲稳保结合</p>
                </div>
            </div>
            
            <div class="recommendation-list">
                <div class="recommendation-item">
                    <div class="school-name">华中科技大学</div>
                    <div class="major-name">计算机科学与技术</div>
                    <span class="probability medium">录取概率: 78%</span>
                    <p class="preview-reason">
                        推荐理由：你的612分超过该专业2024年录取线18分，数学145分显示理工科优势明显，该专业就业率96.8%...
                    </p>
                    <button class="unlock-btn" onclick="showPage('career')">查看详细分析 ¥19.9</button>
                </div>
                
                <div class="recommendation-item">
                    <div class="school-name">武汉大学</div>
                    <div class="major-name">软件工程</div>
                    <span class="probability high">录取概率: 89%</span>
                    <p class="preview-reason">
                        推荐理由：稳妥选择，你的分数超过录取线32分，专业实力强，就业前景好...
                    </p>
                    <button class="unlock-btn" onclick="showPage('career')">查看详细分析 ¥19.9</button>
                </div>
                
                <div class="recommendation-item">
                    <div class="school-name">华中师范大学</div>
                    <div class="major-name">数据科学与大数据技术</div>
                    <span class="probability high">录取概率: 95%</span>
                    <p class="preview-reason">
                        推荐理由：保底选择，新兴专业发展前景广阔，师资力量雄厚...
                    </p>
                    <button class="unlock-btn" onclick="showPage('career')">查看详细分析 ¥19.9</button>
                </div>
            </div>
        </div>
        
        <!-- 职业选择页 -->
        <div class="page" id="career">
            <div class="form-container">
                <h2 class="form-title">选择你的理想职业</h2>
                <p class="form-subtitle">基于推荐专业，选择一个最感兴趣的职业方向，我们将为你模拟完整的人生发展路径</p>
                
                <div class="career-selection">
                    <div class="career-item" onclick="selectCareer(this, 'software-engineer')">
                        <div class="career-title">💻 软件开发工程师</div>
                        <div class="career-info">
                            <span class="salary-range">起薪: 12-20K</span>
                            <span>就业率: 96%</span>
                        </div>
                        <p class="career-desc">负责软件系统设计和开发，技术成长快，就业面广</p>
                    </div>
                    
                    <div class="career-item" onclick="selectCareer(this, 'algorithm-engineer')">
                        <div class="career-title">🔧 算法工程师</div>
                        <div class="career-info">
                            <span class="salary-range">起薪: 20-35K</span>
                            <span>门槛: 较高</span>
                        </div>
                        <p class="career-desc">⚠️ 建议考研提升竞争力，985背景+硕士学历成功率更高</p>
                    </div>
                    
                    <div class="career-item" onclick="selectCareer(this, 'product-manager')">
                        <div class="career-title">💼 产品经理</div>
                        <div class="career-info">
                            <span class="salary-range">起薪: 15-25K</span>
                            <span>发展: 广阔</span>
                        </div>
                        <p class="career-desc">负责产品规划和设计，需要技术背景+商业思维</p>
                    </div>
                    
                    <div class="career-item" onclick="selectCareer(this, 'data-scientist')">
                        <div class="career-title">📊 数据科学家</div>
                        <div class="career-info">
                            <span class="salary-range">起薪: 18-30K</span>
                            <span>趋势: 上升</span>
                        </div>
                        <p class="career-desc">新兴热门职业，需要统计学+编程+业务理解能力</p>
                    </div>
                </div>
                
                <button class="simulate-btn" onclick="showPage('simulation')" id="simulateBtn" disabled>
                    🔮 开始AI人生模拟 (选择职业后解锁)
                </button>
            </div>
        </div>
        
        <!-- AI人生模拟页 -->
        <div class="page" id="simulation">
            <div class="simulation-container">
                <h2 class="form-title">🔮 AI人生模拟报告</h2>
                <p class="form-subtitle">基于你选择的职业方向，AI为你模拟未来15年的发展轨迹</p>
                
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-year">2025-2029年 | 大学阶段</div>
                            <div class="timeline-title">📚 华中科技大学 - 计算机科学与技术</div>
                            <div class="timeline-desc">
                                重点规划：扎实编程基础，参与ACM竞赛，大三争取字节/阿里实习机会。预期GPA: 3.5+，掌握Java、Python、前端框架等技术栈。
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-year">2029-2032年 | 职场初期</div>
                            <div class="timeline-title">💻 软件开发工程师 (中级)</div>
                            <div class="timeline-desc">
                                入职腾讯/字节等大厂，年薪18-25万。主要负责后端开发，积累大型项目经验。这个阶段重点提升系统设计能力和技术深度。
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-year">2032-2035年 | 技术专家</div>
                            <div class="timeline-title">🚀 高级工程师/架构师</div>
                            <div class="timeline-desc">
                                年薪35-50万，负责核心系统架构设计。面临选择：继续技术路线成为技术专家，或转向管理路线。建议根据个人特质决定。
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-year">2035-2040年 | 职业巅峰</div>
                            <div class="timeline-title">⭐ 技术总监/CTO (概率65%)</div>
                            <div class="timeline-desc">
                                年薪60-100万+股权。管理20-50人技术团队，负责公司技术战略。也有35%概率选择创业或加入早期创业公司担任技术合伙人。
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-year">风险提示与建议</div>
                            <div class="timeline-title">⚠️ 需要注意的关键节点</div>
                            <div class="timeline-desc">
                                1. 大学期间实习质量决定起点高低<br>
                                2. 30岁前的技术积累决定职业天花板<br>
                                3. 技术更新快，需要持续学习新技术<br>
                                4. 35岁后面临技术vs管理的选择
                            </div>
                        </div>
                    </div>
                </div>
                
                <div style="background: #f8f9ff; padding: 20px; border-radius: 12px; margin-top: 20px; border-left: 4px solid #667eea;">
                    <h3>💰 收入预测总结</h3>
                    <p>15年总收入预估：<strong style="color: #e74c3c;">600-800万</strong></p>
                    <p>职业发展成功率：<strong style="color: #27ae60;">78%</strong> (基于985背景+技术路线)</p>
                    <p>推荐指数：<strong style="color: #f39c12;">⭐⭐⭐⭐⭐</strong> 非常适合你的能力和兴趣</p>
                </div>
                
                <button class="submit-btn" onclick="showPage('home')">
                    🎉 获取完整报告 ¥199
                </button>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(pageId).classList.add('active');
            
            // 滚动到顶部
            document.getElementById(pageId).scrollTop = 0;
        }
        
        function toggleSelection(element) {
            element.classList.toggle('selected');
        }
        
        function selectCareer(element, careerId) {
            // 移除其他选中状态
            document.querySelectorAll('.career-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 选中当前项
            element.classList.add('selected');
            
            // 启用模拟按钮
            document.getElementById('simulateBtn').disabled = false;
            document.getElementById('simulateBtn').style.opacity = '1';
            document.getElementById('simulateBtn').innerHTML = '🔮 开始AI人生模拟';
        }
        
        // 模拟加载效果
        function showLoading(targetPage) {
            const btn = event.target;
            btn.innerHTML = '🤖 AI正在分析中...';
            btn.disabled = true;
            
            setTimeout(() => {
                showPage(targetPage);
                btn.innerHTML = '🔮 开始AI人生模拟';
                btn.disabled = false;
            }, 2000);
        }
        
        // 表单验证
        function validateForm() {
            const score = document.getElementById('score').value;
            const province = document.getElementById('province').value;
            const subjects = document.getElementById('subjects').value;
            
            if (!score || !province || !subjects) {
                alert('请填写必填项');
                return false;
            }
            
            if (score < 100 || score > 750) {
                alert('请输入有效的分数范围(100-750)');
                return false;
            }
            
            return true;
        }
        
        // 页面切换时的进度更新
        function updateProgress() {
            const progressFill = document.querySelector('.progress-fill');
            const currentPage = document.querySelector('.page.active').id;
            
            switch(currentPage) {
                case 'home':
                    progressFill.style.width = '0%';
                    break;
                case 'form':
                    progressFill.style.width = '25%';
                    break;
                case 'results':
                    progressFill.style.width = '50%';
                    break;
                case 'career':
                    progressFill.style.width = '75%';
                    break;
                case 'simulation':
                    progressFill.style.width = '100%';
                    break;
            }
        }
        
        // 添加页面切换动画
        function showPageWithAnimation(pageId) {
            const currentPage = document.querySelector('.page.active');
            const targetPage = document.getElementById(pageId);
            
            if (currentPage) {
                currentPage.style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    currentPage.classList.remove('active');
                    currentPage.style.transform = '';
                    targetPage.classList.add('active');
                    targetPage.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        targetPage.style.transform = '';
                    }, 50);
                }, 300);
            } else {
                targetPage.classList.add('active');
            }
            
            updateProgress();
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress();
            
            // 为提交按钮添加验证
            document.querySelector('.submit-btn').addEventListener('click', function(e) {
                if (!validateForm()) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>