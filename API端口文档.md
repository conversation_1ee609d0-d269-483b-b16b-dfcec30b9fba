# 志愿AI规划师 - API接口文档

## 1. 接口概述

### 基础信息
- **API Base URL**: `https://api.zhiyuan-ai.com/v1`
- **认证方式**: Bear<PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-06-09T10:30:00Z",
  "request_id": "req_1234567890"
}
```

### 错误码定义
| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

---

## 2. 用户模块

### 2.1 用户注册/登录
```http
POST /auth/login
```

**请求参数**
```json
{
  "phone": "13800138000",
  "verification_code": "123456",
  "invite_code": "optional"
}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "refresh_token_here",
    "user_id": "user_123456",
    "expires_in": 7200
  }
}
```

### 2.2 发送验证码
```http
POST /auth/send-code
```

**请求参数**
```json
{
  "phone": "13800138000",
  "type": "login" // login, register
}
```

---

## 3. 考生信息模块

### 3.1 提交考生基本信息
```http
POST /student/profile
```

**请求参数**
```json
{
  "name": "张三",
  "score": 612,
  "province": "hubei",
  "subject_combination": "physics_chemistry_biology",
  "exam_type": "new_gaokao", // new_gaokao, traditional
  "preferred_regions": ["local", "tier1_cities"],
  "major_preferences": ["engineering", "economics"],
  "family_economic_status": "middle_class",
  "special_requirements": {
    "physical_condition": "normal", // normal, color_blind, etc.
    "family_expectations": "stable_job",
    "personal_interests": ["technology", "innovation"]
  }
}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "profile_id": "profile_123456",
    "predicted_rank": {
      "province_rank": "5.2%",
      "estimated_position": 12580
    },
    "school_tier_prediction": "985", // 985, 211, tier1, tier2
    "status": "completed"
  }
}
```

### 3.2 获取考生信息
```http
GET /student/profile/{profile_id}
```

---

## 4. 志愿推荐模块

### 4.1 生成志愿推荐
```http
POST /recommendation/generate
```

**请求参数**
```json
{
  "profile_id": "profile_123456",
  "recommendation_type": "comprehensive", // comprehensive, conservative, aggressive
  "max_recommendations": 10
}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "recommendation_id": "rec_123456",
    "recommendations": [
      {
        "school_id": "school_001",
        "school_name": "华中科技大学",
        "major_id": "major_001",
        "major_name": "计算机科学与技术",
        "recommendation_type": "rush", // rush, stable, safe
        "admission_probability": 78,
        "score_analysis": {
          "required_score": 594,
          "student_score": 612,
          "score_advantage": 18,
          "historical_data": {
            "2024_min_score": 594,
            "2024_avg_score": 608,
            "2024_max_score": 645
          }
        },
        "preview_reason": "你的612分超过该专业2024年录取线18分，数学145分显示理工科优势明显...",
        "is_unlocked": false,
        "unlock_price": 19.9
      }
    ],
    "summary": {
      "total_count": 8,
      "rush_count": 2,
      "stable_count": 4,
      "safe_count": 2
    }
  }
}
```

### 4.2 解锁详细分析
```http
POST /recommendation/unlock
```

**请求参数**
```json
{
  "recommendation_id": "rec_123456",
  "item_ids": ["school_001_major_001"],
  "payment_token": "payment_token_here"
}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "detailed_analysis": {
      "school_analysis": {
        "school_advantages": ["985工程", "计算机学科全国前5", "就业率高"],
        "location_analysis": "武汉市，新一线城市，互联网产业发达",
        "campus_environment": "校园环境优美，学术氛围浓厚"
      },
      "major_analysis": {
        "curriculum_features": "注重理论与实践结合，与华为、腾讯等企业合作",
        "employment_prospects": {
          "employment_rate": "96.8%",
          "average_salary": "15000-25000元",
          "top_employers": ["华为", "腾讯", "字节跳动", "阿里巴巴"]
        },
        "further_study": {
          "graduate_rate": "34%",
          "top_destinations": ["本校", "清华大学", "北京大学"]
        }
      },
      "match_analysis": {
        "score_match": 92,
        "subject_match": 88,
        "interest_match": 85,
        "overall_match": 88
      },
      "risk_warning": [
        "该专业竞争激烈，建议提前了解专业课程",
        "对数学要求较高，需要持续学习能力"
      ],
      "career_prospects": [
        {
          "career_name": "软件开发工程师",
          "probability": "高",
          "salary_range": "12-25K",
          "development_path": "初级→中级→高级→架构师"
        }
      ]
    }
  }
}
```

---

## 5. 职业规划模块

### 5.1 获取专业对应职业
```http
GET /career/by-major/{major_id}
```

**请求参数**
- `school_tier`: 985/211/tier1/tier2 (Query参数)

**响应数据**
```json
{
  "code": 200,
  "data": {
    "careers": [
      {
        "career_id": "career_001",
        "career_name": "软件开发工程师",
        "career_category": "技术类",
        "entry_probability": {
          "985": "高(85%)",
          "211": "高(75%)",
          "tier1": "中(60%)",
          "tier2": "中(45%)"
        },
        "salary_info": {
          "entry_level": "12-20K",
          "mid_level": "20-35K",
          "senior_level": "35-60K"
        },
        "requirements": {
          "education": "本科",
          "skills": ["编程能力", "逻辑思维", "学习能力"],
          "personality": ["专注", "耐心", "创新"]
        },
        "job_description": "负责软件系统的设计、开发、测试和维护",
        "development_path": "初级工程师→中级工程师→高级工程师→技术专家/管理岗",
        "industry_outlook": "持续增长，人工智能时代需求旺盛"
      }
    ]
  }
}
```

### 5.2 职业匹配度分析
```http
POST /career/match-analysis
```

**请求参数**
```json
{
  "profile_id": "profile_123456",
  "career_id": "career_001",
  "school_tier": "985"
}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "match_score": 87,
    "analysis": {
      "ability_match": {
        "score": 90,
        "details": "数学145分显示逻辑思维强，适合编程工作"
      },
      "interest_match": {
        "score": 85,
        "details": "对技术和创新感兴趣，与软件开发工作契合"
      },
      "background_match": {
        "score": 88,
        "details": "985背景在求职中有明显优势"
      }
    },
    "recommendations": [
      "建议在大学期间重点学习编程技能",
      "参与项目实践，积累作品集",
      "争取大厂实习机会"
    ],
    "risk_factors": [
      "技术更新快，需要持续学习",
      "竞争激烈，需要不断提升技能"
    ]
  }
}
```

---

## 6. AI人生模拟模块

### 6.1 生成人生模拟
```http
POST /simulation/generate
```

**请求参数**
```json
{
  "profile_id": "profile_123456",
  "selected_school": "华中科技大学",
  "selected_major": "计算机科学与技术",
  "target_career": "software_engineer",
  "simulation_years": 15,
  "preferences": {
    "risk_preference": "moderate", // conservative, moderate, aggressive
    "career_priority": "salary", // salary, stability, growth
    "location_preference": "tier1_cities"
  }
}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "simulation_id": "sim_123456",
    "timeline": [
      {
        "year_range": "2025-2029",
        "stage": "university",
        "title": "华中科技大学 - 计算机科学与技术",
        "description": "重点规划：扎实编程基础，参与ACM竞赛，大三争取字节/阿里实习机会",
        "key_metrics": {
          "expected_gpa": "3.5+",
          "skill_development": ["Java", "Python", "前端框架"],
          "milestone_events": ["ACM竞赛", "大厂实习", "毕业设计"]
        },
        "probability": 85
      },
      {
        "year_range": "2029-2032",
        "stage": "early_career",
        "title": "软件开发工程师 (中级)",
        "description": "入职腾讯/字节等大厂，年薪18-25万。主要负责后端开发，积累大型项目经验",
        "key_metrics": {
          "salary_range": "18-25万",
          "company_tier": "大厂",
          "skill_level": "中级",
          "key_achievements": ["参与核心项目", "技术栈深化", "团队协作"]
        },
        "probability": 78
      },
      {
        "year_range": "2032-2035",
        "stage": "career_growth",
        "title": "高级工程师/架构师",
        "description": "年薪35-50万，负责核心系统架构设计。面临选择：继续技术路线成为技术专家，或转向管理路线",
        "key_metrics": {
          "salary_range": "35-50万",
          "responsibility": "系统架构",
          "team_size": "5-10人",
          "decision_point": "技术 vs 管理路线选择"
        },
        "probability": 65,
        "alternatives": [
          {
            "path": "技术专家路线",
            "probability": 60,
            "description": "深耕技术，成为技术专家"
          },
          {
            "path": "管理路线",
            "probability": 40,
            "description": "转向技术管理，带领团队"
          }
        ]
      },
      {
        "year_range": "2035-2040",
        "stage": "career_peak",
        "title": "技术总监/CTO",
        "description": "年薪60-100万+股权。管理20-50人技术团队，负责公司技术战略",
        "key_metrics": {
          "salary_range": "60-100万+股权",
          "team_size": "20-50人",
          "responsibility": "技术战略",
          "industry_influence": "中等"
        },
        "probability": 45,
        "alternatives": [
          {
            "path": "创业",
            "probability": 30,
            "description": "技术创业，担任CTO或创始人"
          },
          {
            "path": "投资转型",
            "probability": 25,
            "description": "转向技术投资，利用行业经验"
          }
        ]
      }
    ],
    "summary": {
      "total_income_prediction": "600-800万",
      "career_success_rate": "78%",
      "key_risk_factors": [
        "技术更新换代风险",
        "35岁职业转型压力",
        "行业竞争加剧"
      ],
      "success_factors": [
        "985背景优势",
        "扎实技术基础",
        "持续学习能力",
        "行业发展机遇"
      ],
      "recommendation_score": 88
    }
  }
}
```

### 6.2 获取模拟详情
```http
GET /simulation/{simulation_id}
```

### 6.3 更新模拟参数
```http
PUT /simulation/{simulation_id}
```

**请求参数**
```json
{
  "preferences": {
    "risk_preference": "aggressive",
    "career_priority": "growth"
  }
}
```

---

## 7. 支付模块

### 7.1 创建订单
```http
POST /payment/create-order
```

**请求参数**
```json
{
  "product_type": "detailed_analysis", // detailed_analysis, simulation_report, premium_package
  "product_ids": ["rec_123456"],
  "amount": 19.9,
  "payment_method": "wechat_pay"
}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "order_id": "order_123456",
    "payment_info": {
      "prepay_id": "prepay_id_from_wechat",
      "sign": "sign_here",
      "timestamp": "1622547000",
      "nonce_str": "random_string"
    },
    "expires_at": "2025-06-09T11:00:00Z"
  }
}
```

### 7.2 查询订单状态
```http
GET /payment/order/{order_id}
```

### 7.3 支付回调
```http
POST /payment/callback/wechat
```

---

## 8. 数据统计模块

### 8.1 获取省份统计数据
```http
GET /statistics/province/{province_code}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "province_name": "湖北",
    "exam_participants": 42000,
    "score_distribution": {
      "600_plus": "5.2%",
      "550_600": "15.8%",
      "500_550": "25.6%"
    },
    "top_universities": [
      {
        "name": "华中科技大学",
        "admission_rate": "2.1%",
        "popular_majors": ["计算机", "电气工程", "机械工程"]
      }
    ]
  }
}
```

### 8.2 获取专业就业数据
```http
GET /statistics/major/{major_id}/employment
```

---

## 9. 系统配置模块

### 9.1 获取系统配置
```http
GET /config/system
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "current_year": 2025,
    "available_provinces": [
      {"code": "hubei", "name": "湖北", "exam_type": "new_gaokao"},
      {"code": "henan", "name": "河南", "exam_type": "traditional"}
    ],
    "subject_combinations": [
      {"code": "physics_chemistry_biology", "name": "物理+化学+生物", "type": "science"},
      {"code": "history_politics_geography", "name": "历史+政治+地理", "type": "liberal_arts"}
    ],
    "pricing": {
      "detailed_analysis": 19.9,
      "simulation_report": 199,
      "premium_package": 499
    },
    "features": {
      "ai_simulation_enabled": true,
      "career_planning_enabled": true,
      "voice_consultation_enabled": false
    }
  }
}
```

---

## 10. 数据库设计

### 10.1 用户表 (users)
```sql
CREATE TABLE users (
  id VARCHAR(50) PRIMARY KEY,
  phone VARCHAR(20) UNIQUE NOT NULL,
  nickname VARCHAR(50),
  avatar_url VARCHAR(200),
  register_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_login_time TIMESTAMP,
  status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
  invite_code VARCHAR(20),
  invited_by VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 10.2 考生档案表 (student_profiles)
```sql
CREATE TABLE student_profiles (
  id VARCHAR(50) PRIMARY KEY,
  user_id VARCHAR(50) NOT NULL,
  name VARCHAR(50) NOT NULL,
  score INT NOT NULL,
  province VARCHAR(20) NOT NULL,
  subject_combination VARCHAR(50) NOT NULL,
  exam_type ENUM('new_gaokao', 'traditional') NOT NULL,
  preferred_regions JSON,
  major_preferences JSON,
  family_economic_status VARCHAR(20),
  special_requirements JSON,
  predicted_rank JSON,
  school_tier_prediction VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 10.3 院校专业表 (universities_majors)
```sql
CREATE TABLE universities_majors (
  id VARCHAR(50) PRIMARY KEY,
  university_id VARCHAR(50) NOT NULL,
  university_name VARCHAR(100) NOT NULL,
  major_id VARCHAR(50) NOT NULL,
  major_name VARCHAR(100) NOT NULL,
  university_tier ENUM('985', '211', 'tier1', 'tier2') NOT NULL,
  province VARCHAR(20) NOT NULL,
  city VARCHAR(50) NOT NULL,
  subject_requirements JSON,
  physical_requirements JSON,
  historical_scores JSON, -- 历年录取分数线
  employment_data JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 10.4 推荐记录表 (recommendations)
```sql
CREATE TABLE recommendations (
  id VARCHAR(50) PRIMARY KEY,
  profile_id VARCHAR(50) NOT NULL,
  recommendation_data JSON NOT NULL,
  recommendation_type VARCHAR(20) NOT NULL,
  generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_paid BOOLEAN DEFAULT FALSE,
  paid_at TIMESTAMP NULL,
  FOREIGN KEY (profile_id) REFERENCES student_profiles(id)
);
```

### 10.5 职业数据表 (careers)
```sql
CREATE TABLE careers (
  id VARCHAR(50) PRIMARY KEY,
  career_name VARCHAR(100) NOT NULL,
  career_category VARCHAR(50) NOT NULL,
  description TEXT,
  requirements JSON,
  salary_data JSON,
  employment_prospects JSON,
  related_majors JSON,
  development_path TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 10.6 模拟记录表 (simulations)
```sql
CREATE TABLE simulations (
  id VARCHAR(50) PRIMARY KEY,
  profile_id VARCHAR(50) NOT NULL,
  selected_university VARCHAR(100) NOT NULL,
  selected_major VARCHAR(100) NOT NULL,
  target_career VARCHAR(100) NOT NULL,
  simulation_parameters JSON,
  simulation_result JSON NOT NULL,
  generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_paid BOOLEAN DEFAULT FALSE,
  paid_at TIMESTAMP NULL,
  FOREIGN KEY (profile_id) REFERENCES student_profiles(id)
);
```

### 10.7 订单表 (orders)
```sql
CREATE TABLE orders (
  id VARCHAR(50) PRIMARY KEY,
  user_id VARCHAR(50) NOT NULL,
  product_type VARCHAR(50) NOT NULL,
  product_ids JSON,
  amount DECIMAL(10,2) NOT NULL,
  payment_method VARCHAR(20) NOT NULL,
  payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
  payment_time TIMESTAMP NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

---

## 11. 部署架构

### 11.1 技术栈
- **后端**: Node.js + Express + TypeScript
- **数据库**: MySQL 8.0 + Redis
- **AI服务**: OpenAI API / Claude API
- **文件存储**: 阿里云OSS
- **消息队列**: Redis + Bull
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

### 11.2 服务器配置
```yaml
# 生产环境配置
production:
  api_server:
    instance_type: "ecs.c6.2xlarge" # 8核16G
    replicas: 3
    auto_scaling: true
    
  database:
    mysql:
      instance_type: "rds.mysql.x8.2xlarge"
      storage: "500GB SSD"
      backup: "daily"
    redis:
      instance_type: "redis.master.2xlarge"
      memory: "16GB"
      
  load_balancer:
    type: "Application Load Balancer"
    ssl_certificate: "wildcard SSL"
    
  cdn:
    provider: "阿里云CDN"
    cache_policy: "aggressive for static assets"
```

---

## 12. 安全措施

### 12.1 API安全
- **Rate Limiting**: 每用户每分钟最多30次请求
- **JWT Token**: 2小时过期，支持刷新
- **参数验证**: 所有输入参数严格验证
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 输出内容转义

### 12.2 数据安全
- **敏感数据加密**: 手机号等PII数据加密存储
- **数据脱敏**: 日志中自动脱敏敏感信息
- **访问控制**: 基于角色的访问控制(RBAC)
- **数据备份**: 每日全量备份，实时增量备份

### 12.3 业务安全
- **支付安全**: 接入微信支付官方SDK
- **防刷机制**: IP限制 + 设备指纹识别
- **数据审计**: 关键操作记录审计日志

---

这个API设计文档涵盖了完整的产品功能，特别是新增的AI人生模拟功能，可以作为前后端开发的详细参考。你觉得哪些部分需要进一步细化？