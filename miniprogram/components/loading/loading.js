// components/loading/loading.js
Component({
  properties: {
    // 是否显示加载
    show: {
      type: Boolean,
      value: false
    },
    // 加载文本
    text: {
      type: String,
      value: '加载中...'
    },
    // 加载图标
    icon: {
      type: String,
      value: '🤖'
    },
    // 是否显示遮罩
    mask: {
      type: Boolean,
      value: true
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  data: {
    dots: ''
  },

  lifetimes: {
    attached() {
      this.startAnimation();
    },
    
    detached() {
      this.stopAnimation();
    }
  },

  observers: {
    'show': function(show) {
      if (show) {
        this.startAnimation();
      } else {
        this.stopAnimation();
      }
    }
  },

  methods: {
    // 开始动画
    startAnimation() {
      if (this.animationTimer) return;
      
      let count = 0;
      this.animationTimer = setInterval(() => {
        count = (count + 1) % 4;
        this.setData({
          dots: '.'.repeat(count)
        });
      }, 500);
    },

    // 停止动画
    stopAnimation() {
      if (this.animationTimer) {
        clearInterval(this.animationTimer);
        this.animationTimer = null;
      }
    },

    // 点击遮罩
    onMaskTap() {
      // 阻止事件冒泡
    }
  }
});
