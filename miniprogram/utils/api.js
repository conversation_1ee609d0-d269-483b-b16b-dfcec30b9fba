// utils/api.js
const app = getApp();

/**
 * API服务类
 */
class ApiService {
  constructor() {
    this.baseUrl = 'https://47.109.58.196/v1';
  }

  /**
   * 通用请求方法
   */
  request(options) {
    const { url, method = 'GET', data = {}, needAuth = true } = options;
    
    return new Promise((resolve, reject) => {
      const header = {
        'Content-Type': 'application/json'
      };
      
      // 添加认证头
      if (needAuth && app.globalData.accessToken) {
        header['Authorization'] = `Bearer ${app.globalData.accessToken}`;
      }
      
      wx.request({
        url: `${this.baseUrl}${url}`,
        method,
        data,
        header,
        success: (res) => {
          if (res.statusCode === 200) {
            if (res.data.code === 200) {
              resolve(res.data);
            } else {
              this.handleApiError(res.data);
              reject(res.data);
            }
          } else if (res.statusCode === 401) {
            this.handleUnauthorized();
            reject(res.data);
          } else {
            this.showError('网络请求失败，请稍后重试');
            reject(res.data);
          }
        },
        fail: (err) => {
          console.error('Request failed:', err);
          this.showError('网络连接失败，请检查网络设置');
          reject(err);
        }
      });
    });
  }

  /**
   * 处理API错误
   */
  handleApiError(data) {
    const errorMessages = {
      400: '请求参数错误',
      401: '未授权访问',
      403: '禁止访问',
      404: '资源不存在',
      429: '请求过于频繁，请稍后重试',
      500: '服务器内部错误'
    };
    
    const message = errorMessages[data.code] || data.message || '未知错误';
    this.showError(message);
  }

  /**
   * 处理未授权
   */
  handleUnauthorized() {
    app.clearLoginStatus();
    wx.showModal({
      title: '登录过期',
      content: '您的登录已过期，请重新登录',
      showCancel: false,
      success: () => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }
    });
  }

  /**
   * 显示错误信息
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  }

  // ========== 用户相关API ==========

  /**
   * 发送验证码
   */
  sendVerificationCode(phone, type = 'login') {
    return this.request({
      url: '/auth/send-code',
      method: 'POST',
      data: { phone, type },
      needAuth: false
    });
  }

  /**
   * 用户登录
   */
  login(phone, verificationCode, inviteCode) {
    return this.request({
      url: '/auth/login',
      method: 'POST',
      data: {
        phone,
        verification_code: verificationCode,
        invite_code: inviteCode
      },
      needAuth: false
    });
  }

  /**
   * 微信登录
   */
  wechatLogin(code, inviteCode) {
    return this.request({
      url: '/auth/wechat-login',
      method: 'POST',
      data: { code, invite_code: inviteCode },
      needAuth: false
    });
  }

  // ========== 学生档案相关API ==========

  /**
   * 提交学生档案
   */
  submitStudentProfile(profileData) {
    return this.request({
      url: '/student/profile',
      method: 'POST',
      data: profileData
    });
  }

  /**
   * 获取学生档案
   */
  getStudentProfile(profileId) {
    return this.request({
      url: `/student/profile/${profileId}`,
      method: 'GET'
    });
  }

  // ========== 推荐相关API ==========

  /**
   * 生成志愿推荐
   */
  generateRecommendations(profileId, type = 'comprehensive', maxCount = 10) {
    return this.request({
      url: '/recommendation/generate',
      method: 'POST',
      data: {
        profile_id: profileId,
        recommendation_type: type,
        max_recommendations: maxCount
      }
    });
  }

  /**
   * 解锁详细分析
   */
  unlockRecommendation(recommendationId, itemIds, paymentToken) {
    return this.request({
      url: '/recommendation/unlock',
      method: 'POST',
      data: {
        recommendation_id: recommendationId,
        item_ids: itemIds,
        payment_token: paymentToken
      }
    });
  }

  // ========== 职业相关API ==========

  /**
   * 获取专业对应职业
   */
  getCareersByMajor(majorId, schoolTier) {
    return this.request({
      url: `/career/by-major/${majorId}`,
      method: 'GET',
      data: { school_tier: schoolTier }
    });
  }

  /**
   * 职业匹配度分析
   */
  analyzeCareerMatch(profileId, careerId, schoolTier) {
    return this.request({
      url: '/career/match-analysis',
      method: 'POST',
      data: {
        profile_id: profileId,
        career_id: careerId,
        school_tier: schoolTier
      }
    });
  }

  // ========== AI模拟相关API ==========

  /**
   * 生成人生模拟
   */
  generateSimulation(data) {
    return this.request({
      url: '/simulation/generate',
      method: 'POST',
      data
    });
  }

  /**
   * 获取模拟详情
   */
  getSimulation(simulationId) {
    return this.request({
      url: `/simulation/${simulationId}`,
      method: 'GET'
    });
  }

  /**
   * 更新模拟参数
   */
  updateSimulation(simulationId, preferences) {
    return this.request({
      url: `/simulation/${simulationId}`,
      method: 'PUT',
      data: { preferences }
    });
  }

  // ========== 支付相关API ==========

  /**
   * 创建订单
   */
  createOrder(productType, productIds, amount, paymentMethod = 'wechat_pay') {
    return this.request({
      url: '/payment/create-order',
      method: 'POST',
      data: {
        product_type: productType,
        product_ids: productIds,
        amount,
        payment_method: paymentMethod
      }
    });
  }

  /**
   * 查询订单状态
   */
  getOrderStatus(orderId) {
    return this.request({
      url: `/payment/order/${orderId}`,
      method: 'GET'
    });
  }

  // ========== 系统配置相关API ==========

  /**
   * 获取系统配置
   */
  getSystemConfig() {
    return this.request({
      url: '/config/system',
      method: 'GET',
      needAuth: false
    });
  }

  /**
   * 获取省份统计数据
   */
  getProvinceStats(provinceCode) {
    return this.request({
      url: `/statistics/province/${provinceCode}`,
      method: 'GET',
      needAuth: false
    });
  }
}

// 创建单例
const apiService = new ApiService();

module.exports = apiService;
