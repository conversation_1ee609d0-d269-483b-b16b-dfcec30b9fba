// utils/validator.js

const { VALIDATION_RULES } = require('./constants');

/**
 * 数据验证工具类
 */
class Validator {
  constructor() {
    this.rules = {};
    this.messages = {};
  }

  /**
   * 添加验证规则
   * @param {string} field 字段名
   * @param {array} rules 规则数组
   * @param {string} message 错误消息
   */
  addRule(field, rules, message) {
    this.rules[field] = rules;
    this.messages[field] = message;
    return this;
  }

  /**
   * 验证数据
   * @param {object} data 要验证的数据
   * @returns {object} 验证结果
   */
  validate(data) {
    const errors = {};
    let isValid = true;

    for (const field in this.rules) {
      const value = data[field];
      const rules = this.rules[field];
      const message = this.messages[field];

      for (const rule of rules) {
        const result = this.applyRule(value, rule);
        if (!result.valid) {
          errors[field] = message || result.message;
          isValid = false;
          break;
        }
      }
    }

    return {
      isValid,
      errors,
      firstError: isValid ? null : Object.values(errors)[0]
    };
  }

  /**
   * 应用单个验证规则
   * @param {any} value 值
   * @param {object|function} rule 规则
   * @returns {object} 验证结果
   */
  applyRule(value, rule) {
    if (typeof rule === 'function') {
      return rule(value);
    }

    const { type, params = [], message } = rule;

    switch (type) {
      case 'required':
        return this.validateRequired(value, message);
      case 'minLength':
        return this.validateMinLength(value, params[0], message);
      case 'maxLength':
        return this.validateMaxLength(value, params[0], message);
      case 'min':
        return this.validateMin(value, params[0], message);
      case 'max':
        return this.validateMax(value, params[0], message);
      case 'pattern':
        return this.validatePattern(value, params[0], message);
      case 'email':
        return this.validateEmail(value, message);
      case 'phone':
        return this.validatePhone(value, message);
      case 'number':
        return this.validateNumber(value, message);
      case 'integer':
        return this.validateInteger(value, message);
      case 'array':
        return this.validateArray(value, message);
      case 'object':
        return this.validateObject(value, message);
      default:
        return { valid: true };
    }
  }

  /**
   * 验证必填
   */
  validateRequired(value, message = '此字段为必填项') {
    const valid = value !== null && value !== undefined && value !== '';
    return { valid, message };
  }

  /**
   * 验证最小长度
   */
  validateMinLength(value, minLength, message = `最少需要${minLength}个字符`) {
    if (value === null || value === undefined) {
      return { valid: true };
    }
    const valid = String(value).length >= minLength;
    return { valid, message };
  }

  /**
   * 验证最大长度
   */
  validateMaxLength(value, maxLength, message = `最多允许${maxLength}个字符`) {
    if (value === null || value === undefined) {
      return { valid: true };
    }
    const valid = String(value).length <= maxLength;
    return { valid, message };
  }

  /**
   * 验证最小值
   */
  validateMin(value, min, message = `值不能小于${min}`) {
    if (value === null || value === undefined || value === '') {
      return { valid: true };
    }
    const valid = Number(value) >= min;
    return { valid, message };
  }

  /**
   * 验证最大值
   */
  validateMax(value, max, message = `值不能大于${max}`) {
    if (value === null || value === undefined || value === '') {
      return { valid: true };
    }
    const valid = Number(value) <= max;
    return { valid, message };
  }

  /**
   * 验证正则表达式
   */
  validatePattern(value, pattern, message = '格式不正确') {
    if (value === null || value === undefined || value === '') {
      return { valid: true };
    }
    const valid = pattern.test(String(value));
    return { valid, message };
  }

  /**
   * 验证邮箱
   */
  validateEmail(value, message = '请输入正确的邮箱地址') {
    return this.validatePattern(value, VALIDATION_RULES.EMAIL, message);
  }

  /**
   * 验证手机号
   */
  validatePhone(value, message = '请输入正确的手机号') {
    return this.validatePattern(value, VALIDATION_RULES.PHONE, message);
  }

  /**
   * 验证数字
   */
  validateNumber(value, message = '请输入有效的数字') {
    if (value === null || value === undefined || value === '') {
      return { valid: true };
    }
    const valid = !isNaN(Number(value));
    return { valid, message };
  }

  /**
   * 验证整数
   */
  validateInteger(value, message = '请输入整数') {
    if (value === null || value === undefined || value === '') {
      return { valid: true };
    }
    const valid = Number.isInteger(Number(value));
    return { valid, message };
  }

  /**
   * 验证数组
   */
  validateArray(value, message = '必须是数组类型') {
    const valid = Array.isArray(value);
    return { valid, message };
  }

  /**
   * 验证对象
   */
  validateObject(value, message = '必须是对象类型') {
    const valid = typeof value === 'object' && value !== null && !Array.isArray(value);
    return { valid, message };
  }

  /**
   * 清除所有规则
   */
  clear() {
    this.rules = {};
    this.messages = {};
    return this;
  }
}

/**
 * 创建验证器实例
 */
const createValidator = () => new Validator();

/**
 * 常用验证规则
 */
const CommonRules = {
  required: { type: 'required' },
  email: { type: 'email' },
  phone: { type: 'phone' },
  number: { type: 'number' },
  integer: { type: 'integer' },
  
  minLength: (length) => ({ type: 'minLength', params: [length] }),
  maxLength: (length) => ({ type: 'maxLength', params: [length] }),
  min: (value) => ({ type: 'min', params: [value] }),
  max: (value) => ({ type: 'max', params: [value] }),
  pattern: (regex) => ({ type: 'pattern', params: [regex] }),

  // 自定义验证规则
  score: (value) => {
    if (value === null || value === undefined || value === '') {
      return { valid: true };
    }
    const num = Number(value);
    const valid = num >= VALIDATION_RULES.SCORE_MIN && num <= VALIDATION_RULES.SCORE_MAX;
    return {
      valid,
      message: `分数必须在${VALIDATION_RULES.SCORE_MIN}-${VALIDATION_RULES.SCORE_MAX}之间`
    };
  },

  name: (value) => {
    if (!value || value.trim() === '') {
      return { valid: false, message: '姓名不能为空' };
    }
    const trimmed = value.trim();
    if (trimmed.length < VALIDATION_RULES.NAME_MIN_LENGTH) {
      return { valid: false, message: `姓名至少需要${VALIDATION_RULES.NAME_MIN_LENGTH}个字符` };
    }
    if (trimmed.length > VALIDATION_RULES.NAME_MAX_LENGTH) {
      return { valid: false, message: `姓名不能超过${VALIDATION_RULES.NAME_MAX_LENGTH}个字符` };
    }
    return { valid: true };
  },

  verificationCode: (value) => {
    if (!value || value.trim() === '') {
      return { valid: false, message: '验证码不能为空' };
    }
    const valid = value.trim().length === VALIDATION_RULES.CODE_LENGTH;
    return {
      valid,
      message: `验证码必须是${VALIDATION_RULES.CODE_LENGTH}位数字`
    };
  }
};

/**
 * 快速验证函数
 */
const quickValidate = {
  /**
   * 验证手机号
   */
  phone: (phone) => {
    return VALIDATION_RULES.PHONE.test(phone);
  },

  /**
   * 验证邮箱
   */
  email: (email) => {
    return VALIDATION_RULES.EMAIL.test(email);
  },

  /**
   * 验证分数
   */
  score: (score) => {
    const num = Number(score);
    return num >= VALIDATION_RULES.SCORE_MIN && num <= VALIDATION_RULES.SCORE_MAX;
  },

  /**
   * 验证姓名
   */
  name: (name) => {
    if (!name || name.trim() === '') return false;
    const trimmed = name.trim();
    return trimmed.length >= VALIDATION_RULES.NAME_MIN_LENGTH && 
           trimmed.length <= VALIDATION_RULES.NAME_MAX_LENGTH;
  },

  /**
   * 验证验证码
   */
  verificationCode: (code) => {
    return code && code.trim().length === VALIDATION_RULES.CODE_LENGTH;
  }
};

module.exports = {
  Validator,
  createValidator,
  CommonRules,
  quickValidate
};
