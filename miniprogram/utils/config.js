// utils/config.js

/**
 * 应用配置
 */
const config = {
  // API配置
  api: {
    baseUrl: 'https://api.zhiyuan-ai.com/v1',
    timeout: 10000
  },

  // 支付配置
  payment: {
    detailedAnalysisPrice: 19.9,
    simulationReportPrice: 199,
    premiumPackagePrice: 499
  },

  // 省份列表
  provinces: [
    { code: 'beijing', name: '北京', examType: 'new_gaokao' },
    { code: 'shanghai', name: '上海', examType: 'new_gaokao' },
    { code: 'tianjin', name: '天津', examType: 'new_gaokao' },
    { code: 'chongqing', name: '重庆', examType: 'new_gaokao' },
    { code: 'hebei', name: '河北', examType: 'new_gaokao' },
    { code: 'shanxi', name: '山西', examType: 'traditional' },
    { code: 'liaoning', name: '辽宁', examType: 'new_gaokao' },
    { code: 'jilin', name: '吉林', examType: 'traditional' },
    { code: 'heilongjiang', name: '黑龙江', examType: 'traditional' },
    { code: 'jiangsu', name: '江苏', examType: 'new_gaokao' },
    { code: 'zhejiang', name: '浙江', examType: 'new_gaokao' },
    { code: 'anhui', name: '安徽', examType: 'traditional' },
    { code: 'fujian', name: '福建', examType: 'new_gaokao' },
    { code: 'jiangxi', name: '江西', examType: 'traditional' },
    { code: 'shandong', name: '山东', examType: 'new_gaokao' },
    { code: 'henan', name: '河南', examType: 'traditional' },
    { code: 'hubei', name: '湖北', examType: 'new_gaokao' },
    { code: 'hunan', name: '湖南', examType: 'new_gaokao' },
    { code: 'guangdong', name: '广东', examType: 'new_gaokao' },
    { code: 'guangxi', name: '广西', examType: 'traditional' },
    { code: 'hainan', name: '海南', examType: 'new_gaokao' },
    { code: 'sichuan', name: '四川', examType: 'traditional' },
    { code: 'guizhou', name: '贵州', examType: 'traditional' },
    { code: 'yunnan', name: '云南', examType: 'traditional' },
    { code: 'xizang', name: '西藏', examType: 'traditional' },
    { code: 'shaanxi', name: '陕西', examType: 'traditional' },
    { code: 'gansu', name: '甘肃', examType: 'traditional' },
    { code: 'qinghai', name: '青海', examType: 'traditional' },
    { code: 'ningxia', name: '宁夏', examType: 'traditional' },
    { code: 'xinjiang', name: '新疆', examType: 'traditional' }
  ],

  // 选科组合
  subjectCombinations: {
    new_gaokao: [
      { code: 'physics_chemistry_biology', name: '物理+化学+生物', type: 'science' },
      { code: 'physics_chemistry_geography', name: '物理+化学+地理', type: 'science' },
      { code: 'physics_chemistry_politics', name: '物理+化学+政治', type: 'science' },
      { code: 'physics_biology_geography', name: '物理+生物+地理', type: 'science' },
      { code: 'physics_biology_politics', name: '物理+生物+政治', type: 'science' },
      { code: 'physics_geography_politics', name: '物理+地理+政治', type: 'mixed' },
      { code: 'history_chemistry_biology', name: '历史+化学+生物', type: 'mixed' },
      { code: 'history_chemistry_geography', name: '历史+化学+地理', type: 'mixed' },
      { code: 'history_chemistry_politics', name: '历史+化学+政治', type: 'mixed' },
      { code: 'history_biology_geography', name: '历史+生物+地理', type: 'mixed' },
      { code: 'history_biology_politics', name: '历史+生物+政治', type: 'mixed' },
      { code: 'history_geography_politics', name: '历史+地理+政治', type: 'liberal_arts' }
    ],
    traditional: [
      { code: 'science', name: '理科', type: 'science' },
      { code: 'liberal_arts', name: '文科', type: 'liberal_arts' }
    ]
  },

  // 地区选项
  regionOptions: [
    { code: 'unlimited', name: '不限', priority: 1 },
    { code: 'local', name: '本省', priority: 2 },
    { code: 'tier1_cities', name: '北上广深', priority: 3 },
    { code: 'new_tier1', name: '新一线城市', priority: 4 },
    { code: 'tier2_cities', name: '二线城市', priority: 5 },
    { code: 'tier3_cities', name: '三线城市', priority: 6 }
  ],

  // 专业类别
  majorCategories: [
    { code: 'engineering', name: '理工类', icon: '🔧' },
    { code: 'economics', name: '经管类', icon: '💼' },
    { code: 'liberal_arts', name: '文史类', icon: '📚' },
    { code: 'medicine', name: '医学类', icon: '⚕️' },
    { code: 'arts', name: '艺术类', icon: '🎨' },
    { code: 'education', name: '教育类', icon: '👨‍🏫' },
    { code: 'law', name: '法学类', icon: '⚖️' },
    { code: 'agriculture', name: '农学类', icon: '🌾' }
  ],

  // 兴趣选项
  interestOptions: [
    { code: 'technology', name: '科技创新', icon: '💻' },
    { code: 'business', name: '商业管理', icon: '📈' },
    { code: 'art_design', name: '艺术设计', icon: '🎨' },
    { code: 'social_service', name: '社会服务', icon: '🤝' },
    { code: 'research', name: '学术研究', icon: '🔬' },
    { code: 'education', name: '教育培训', icon: '📖' },
    { code: 'healthcare', name: '医疗健康', icon: '🏥' },
    { code: 'media', name: '传媒娱乐', icon: '📺' }
  ],

  // 家庭经济状况
  economicStatus: [
    { code: 'low_income', name: '低收入家庭', desc: '年收入5万以下' },
    { code: 'middle_class', name: '中等收入家庭', desc: '年收入5-20万' },
    { code: 'high_income', name: '高收入家庭', desc: '年收入20万以上' }
  ],

  // 学校层次
  schoolTiers: [
    { code: '985', name: '985工程', desc: '顶尖大学' },
    { code: '211', name: '211工程', desc: '重点大学' },
    { code: 'tier1', name: '一本院校', desc: '普通一本' },
    { code: 'tier2', name: '二本院校', desc: '普通二本' }
  ],

  // 推荐类型
  recommendationTypes: [
    { code: 'rush', name: '冲刺', color: '#e74c3c', desc: '录取概率较低，但值得一试' },
    { code: 'stable', name: '稳妥', color: '#f39c12', desc: '录取概率较高，比较稳妥' },
    { code: 'safe', name: '保底', color: '#27ae60', desc: '录取概率很高，确保录取' }
  ],

  // 职业类别
  careerCategories: [
    { code: 'technology', name: '技术类', icon: '💻' },
    { code: 'management', name: '管理类', icon: '👔' },
    { code: 'sales', name: '销售类', icon: '📊' },
    { code: 'design', name: '设计类', icon: '🎨' },
    { code: 'education', name: '教育类', icon: '👨‍🏫' },
    { code: 'healthcare', name: '医疗类', icon: '⚕️' },
    { code: 'finance', name: '金融类', icon: '💰' },
    { code: 'media', name: '传媒类', icon: '📺' }
  ],

  // 风险偏好
  riskPreferences: [
    { code: 'conservative', name: '保守型', desc: '追求稳定，规避风险' },
    { code: 'moderate', name: '平衡型', desc: '平衡收益与风险' },
    { code: 'aggressive', name: '激进型', desc: '追求高收益，承担高风险' }
  ],

  // 职业优先级
  careerPriorities: [
    { code: 'salary', name: '薪资优先', desc: '追求高薪资收入' },
    { code: 'stability', name: '稳定优先', desc: '追求工作稳定性' },
    { code: 'growth', name: '发展优先', desc: '追求职业发展空间' },
    { code: 'interest', name: '兴趣优先', desc: '追求工作兴趣匹配' }
  ],

  // 地区偏好
  locationPreferences: [
    { code: 'tier1_cities', name: '一线城市', desc: '北上广深等一线城市' },
    { code: 'new_tier1', name: '新一线城市', desc: '成都、杭州、武汉等' },
    { code: 'tier2_cities', name: '二线城市', desc: '省会城市和重要城市' },
    { code: 'hometown', name: '家乡发展', desc: '回到家乡所在城市' }
  ]
};

module.exports = config;
