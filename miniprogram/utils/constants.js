// utils/constants.js

/**
 * 应用常量定义
 */

// 存储键名
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  STUDENT_PROFILE: 'student_profile',
  FORM_DRAFT: 'form_draft',
  RECOMMENDATION_COUNT: 'recommendation_count',
  SIMULATION_COUNT: 'simulation_count',
  TOTAL_SPENT: 'total_spent',
  SYSTEM_CONFIG: 'system_config'
};

// API状态码
export const API_CODES = {
  SUCCESS: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_ERROR: 500
};

// 页面路径
export const PAGE_PATHS = {
  HOME: '/pages/home/<USER>',
  LOGIN: '/pages/login/login',
  FORM: '/pages/form/form',
  RESULTS: '/pages/results/results',
  CAREER: '/pages/career/career',
  SIMULATION: '/pages/simulation/simulation',
  PROFILE: '/pages/profile/profile'
};

// 事件名称
export const EVENT_NAMES = {
  LOGIN_SUCCESS: 'loginSuccess',
  LOGOUT: 'logout',
  PROFILE_UPDATED: 'profileUpdated',
  RECOMMENDATION_GENERATED: 'recommendationGenerated',
  SIMULATION_GENERATED: 'simulationGenerated',
  PAYMENT_SUCCESS: 'paymentSuccess'
};

// 表单验证规则
export const VALIDATION_RULES = {
  PHONE: /^1[3-9]\d{9}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  SCORE_MIN: 100,
  SCORE_MAX: 750,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 10,
  CODE_LENGTH: 6
};

// 分数等级
export const SCORE_LEVELS = {
  EXCELLENT: { min: 650, max: 750, name: '优秀', color: '#e74c3c' },
  GOOD: { min: 600, max: 649, name: '良好', color: '#f39c12' },
  AVERAGE: { min: 550, max: 599, name: '中等', color: '#3498db' },
  BELOW_AVERAGE: { min: 500, max: 549, name: '一般', color: '#95a5a6' },
  POOR: { min: 100, max: 499, name: '较差', color: '#7f8c8d' }
};

// 录取概率等级
export const PROBABILITY_LEVELS = {
  HIGH: { min: 80, max: 100, name: '很高', color: '#27ae60', bgColor: '#d4edda' },
  MEDIUM: { min: 60, max: 79, name: '中等', color: '#f39c12', bgColor: '#fff3cd' },
  LOW: { min: 40, max: 59, name: '较低', color: '#e74c3c', bgColor: '#f8d7da' },
  VERY_LOW: { min: 0, max: 39, name: '很低', color: '#95a5a6', bgColor: '#f1f3f4' }
};

// 推荐策略
export const RECOMMENDATION_STRATEGIES = {
  CONSERVATIVE: 'conservative',
  BALANCED: 'balanced',
  AGGRESSIVE: 'aggressive'
};

// 支付状态
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PAID: 'paid',
  FAILED: 'failed',
  REFUNDED: 'refunded',
  CANCELLED: 'cancelled'
};

// 产品类型
export const PRODUCT_TYPES = {
  DETAILED_ANALYSIS: 'detailed_analysis',
  SIMULATION_REPORT: 'simulation_report',
  PREMIUM_PACKAGE: 'premium_package',
  CONSULTATION: 'consultation'
};

// 模拟阶段
export const SIMULATION_STAGES = {
  UNIVERSITY: 'university',
  EARLY_CAREER: 'early_career',
  CAREER_GROWTH: 'career_growth',
  CAREER_PEAK: 'career_peak',
  RETIREMENT: 'retirement'
};

// 职业发展路径
export const CAREER_PATHS = {
  TECHNICAL: 'technical',
  MANAGEMENT: 'management',
  ENTREPRENEURSHIP: 'entrepreneurship',
  ACADEMIC: 'academic',
  FREELANCE: 'freelance'
};

// 成功因素类型
export const SUCCESS_FACTORS = {
  EDUCATION: 'education',
  SKILLS: 'skills',
  EXPERIENCE: 'experience',
  NETWORK: 'network',
  OPPORTUNITY: 'opportunity',
  PERSONALITY: 'personality'
};

// 风险因素类型
export const RISK_FACTORS = {
  MARKET: 'market',
  TECHNOLOGY: 'technology',
  COMPETITION: 'competition',
  HEALTH: 'health',
  ECONOMIC: 'economic',
  POLICY: 'policy'
};

// 匹配度等级
export const MATCH_LEVELS = {
  EXCELLENT: { min: 90, max: 100, name: '非常匹配', color: '#27ae60' },
  GOOD: { min: 80, max: 89, name: '比较匹配', color: '#2ecc71' },
  AVERAGE: { min: 70, max: 79, name: '一般匹配', color: '#f39c12' },
  POOR: { min: 60, max: 69, name: '匹配度低', color: '#e67e22' },
  VERY_POOR: { min: 0, max: 59, name: '不匹配', color: '#e74c3c' }
};

// 时间常量
export const TIME_CONSTANTS = {
  COUNTDOWN_SECONDS: 60,
  REQUEST_TIMEOUT: 10000,
  DEBOUNCE_DELAY: 300,
  THROTTLE_DELAY: 1000,
  ANIMATION_DURATION: 300,
  TOAST_DURATION: 2000,
  LOADING_MIN_TIME: 1000
};

// 分页常量
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 50,
  DEFAULT_PAGE: 1
};

// 文件上传常量
export const UPLOAD_CONSTANTS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  ALLOWED_DOCUMENT_TYPES: ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
  MAX_IMAGE_COUNT: 9,
  MAX_DOCUMENT_COUNT: 5
};

// 分享常量
export const SHARE_CONSTANTS = {
  DEFAULT_TITLE: 'AI志愿规划师 - 科学规划你的人生',
  DEFAULT_DESC: '专业的高考志愿填报指导平台，让每个选择都有科学依据',
  DEFAULT_IMAGE: '/images/share-cover.png'
};

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  SERVER_ERROR: '服务器繁忙，请稍后重试',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '没有权限访问该资源',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '输入信息有误，请检查后重试',
  PAYMENT_ERROR: '支付失败，请重试',
  UPLOAD_ERROR: '文件上传失败，请重试'
};

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '已退出登录',
  SAVE_SUCCESS: '保存成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_SUCCESS: '删除成功',
  SUBMIT_SUCCESS: '提交成功',
  PAYMENT_SUCCESS: '支付成功',
  COPY_SUCCESS: '已复制到剪贴板',
  SHARE_SUCCESS: '分享成功'
};

// 默认值
export const DEFAULT_VALUES = {
  AVATAR: '/images/default-avatar.png',
  NICKNAME: '用户',
  EXAM_TYPE: 'new_gaokao',
  ECONOMIC_STATUS: 'middle_class',
  PHYSICAL_CONDITION: 'normal',
  RISK_PREFERENCE: 'moderate',
  CAREER_PRIORITY: 'salary',
  LOCATION_PREFERENCE: 'tier1_cities',
  SIMULATION_YEARS: 15,
  RECOMMENDATION_COUNT: 10
};

module.exports = {
  STORAGE_KEYS,
  API_CODES,
  PAGE_PATHS,
  EVENT_NAMES,
  VALIDATION_RULES,
  SCORE_LEVELS,
  PROBABILITY_LEVELS,
  RECOMMENDATION_STRATEGIES,
  PAYMENT_STATUS,
  PRODUCT_TYPES,
  SIMULATION_STAGES,
  CAREER_PATHS,
  SUCCESS_FACTORS,
  RISK_FACTORS,
  MATCH_LEVELS,
  TIME_CONSTANTS,
  PAGINATION,
  UPLOAD_CONSTANTS,
  SHARE_CONSTANTS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  DEFAULT_VALUES
};
