// utils/error-handler.js

const { API_CODES, ERROR_MESSAGES } = require('./constants');

/**
 * 错误处理工具类
 */
class ErrorHandler {
  constructor() {
    this.errorLog = [];
    this.maxLogSize = 100;
  }

  /**
   * 处理API错误
   * @param {object} error 错误对象
   * @param {object} options 选项
   */
  handleApiError(error, options = {}) {
    const { showToast = true, logError = true } = options;
    
    let message = ERROR_MESSAGES.SERVER_ERROR;
    let code = error.code || error.statusCode;

    // 根据错误码确定错误消息
    switch (code) {
      case API_CODES.BAD_REQUEST:
        message = error.message || ERROR_MESSAGES.VALIDATION_ERROR;
        break;
      case API_CODES.UNAUTHORIZED:
        message = ERROR_MESSAGES.UNAUTHORIZED;
        this.handleUnauthorized();
        break;
      case API_CODES.FORBIDDEN:
        message = ERROR_MESSAGES.FORBIDDEN;
        break;
      case API_CODES.NOT_FOUND:
        message = ERROR_MESSAGES.NOT_FOUND;
        break;
      case API_CODES.TOO_MANY_REQUESTS:
        message = '请求过于频繁，请稍后重试';
        break;
      case API_CODES.INTERNAL_ERROR:
        message = ERROR_MESSAGES.SERVER_ERROR;
        break;
      default:
        if (error.message) {
          message = error.message;
        }
    }

    // 记录错误日志
    if (logError) {
      this.logError('API_ERROR', {
        code,
        message,
        url: error.url,
        method: error.method,
        timestamp: new Date().toISOString(),
        stack: error.stack
      });
    }

    // 显示错误提示
    if (showToast) {
      this.showErrorToast(message);
    }

    return { code, message };
  }

  /**
   * 处理网络错误
   * @param {object} error 错误对象
   * @param {object} options 选项
   */
  handleNetworkError(error, options = {}) {
    const { showToast = true, logError = true } = options;
    const message = ERROR_MESSAGES.NETWORK_ERROR;

    if (logError) {
      this.logError('NETWORK_ERROR', {
        message: error.message || message,
        timestamp: new Date().toISOString(),
        stack: error.stack
      });
    }

    if (showToast) {
      this.showErrorToast(message);
    }

    return { message };
  }

  /**
   * 处理支付错误
   * @param {object} error 错误对象
   * @param {object} options 选项
   */
  handlePaymentError(error, options = {}) {
    const { showToast = true, logError = true } = options;
    let message = ERROR_MESSAGES.PAYMENT_ERROR;

    // 根据支付错误类型确定消息
    if (error.errMsg) {
      if (error.errMsg.includes('cancel')) {
        message = '支付已取消';
      } else if (error.errMsg.includes('fail')) {
        message = '支付失败，请重试';
      }
    }

    if (logError) {
      this.logError('PAYMENT_ERROR', {
        errMsg: error.errMsg,
        errCode: error.errCode,
        timestamp: new Date().toISOString()
      });
    }

    if (showToast) {
      this.showErrorToast(message);
    }

    return { message };
  }

  /**
   * 处理文件上传错误
   * @param {object} error 错误对象
   * @param {object} options 选项
   */
  handleUploadError(error, options = {}) {
    const { showToast = true, logError = true } = options;
    const message = ERROR_MESSAGES.UPLOAD_ERROR;

    if (logError) {
      this.logError('UPLOAD_ERROR', {
        message: error.message || message,
        timestamp: new Date().toISOString(),
        stack: error.stack
      });
    }

    if (showToast) {
      this.showErrorToast(message);
    }

    return { message };
  }

  /**
   * 处理未授权错误
   */
  handleUnauthorized() {
    // 清除登录状态
    const app = getApp();
    if (app && app.clearLoginStatus) {
      app.clearLoginStatus();
    }

    // 延迟跳转到登录页，避免与当前操作冲突
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/login/login'
      });
    }, 1000);
  }

  /**
   * 显示错误提示
   * @param {string} message 错误消息
   */
  showErrorToast(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  }

  /**
   * 记录错误日志
   * @param {string} type 错误类型
   * @param {object} details 错误详情
   */
  logError(type, details) {
    const errorInfo = {
      type,
      details,
      timestamp: new Date().toISOString(),
      userAgent: wx.getSystemInfoSync(),
      page: getCurrentPages().pop()?.route || 'unknown'
    };

    // 添加到错误日志
    this.errorLog.unshift(errorInfo);

    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // 输出到控制台
    console.error(`[${type}]`, details);

    // 可以在这里添加上报到服务器的逻辑
    this.reportError(errorInfo);
  }

  /**
   * 上报错误到服务器
   * @param {object} errorInfo 错误信息
   */
  reportError(errorInfo) {
    // 这里可以实现错误上报逻辑
    // 例如发送到错误监控服务
    try {
      // 示例：上报到服务器
      // wx.request({
      //   url: 'https://api.zhiyuan-ai.com/v1/error/report',
      //   method: 'POST',
      //   data: errorInfo,
      //   fail: () => {
      //     // 上报失败时不做处理，避免循环错误
      //   }
      // });
    } catch (e) {
      // 忽略上报错误
    }
  }

  /**
   * 获取错误日志
   * @param {number} limit 限制数量
   * @returns {array} 错误日志数组
   */
  getErrorLog(limit = 10) {
    return this.errorLog.slice(0, limit);
  }

  /**
   * 清除错误日志
   */
  clearErrorLog() {
    this.errorLog = [];
  }

  /**
   * 创建错误对象
   * @param {string} message 错误消息
   * @param {string} code 错误码
   * @param {object} details 错误详情
   * @returns {Error} 错误对象
   */
  createError(message, code = 'UNKNOWN_ERROR', details = {}) {
    const error = new Error(message);
    error.code = code;
    error.details = details;
    error.timestamp = new Date().toISOString();
    return error;
  }

  /**
   * 包装异步函数，自动处理错误
   * @param {function} fn 异步函数
   * @param {object} options 选项
   * @returns {function} 包装后的函数
   */
  wrapAsync(fn, options = {}) {
    return async (...args) => {
      try {
        return await fn(...args);
      } catch (error) {
        this.handleApiError(error, options);
        throw error;
      }
    };
  }

  /**
   * 包装Promise，自动处理错误
   * @param {Promise} promise Promise对象
   * @param {object} options 选项
   * @returns {Promise} 包装后的Promise
   */
  wrapPromise(promise, options = {}) {
    return promise.catch(error => {
      this.handleApiError(error, options);
      throw error;
    });
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler();

/**
 * 全局错误处理函数
 */
const handleError = (error, type = 'GENERAL', options = {}) => {
  switch (type) {
    case 'API':
      return errorHandler.handleApiError(error, options);
    case 'NETWORK':
      return errorHandler.handleNetworkError(error, options);
    case 'PAYMENT':
      return errorHandler.handlePaymentError(error, options);
    case 'UPLOAD':
      return errorHandler.handleUploadError(error, options);
    default:
      return errorHandler.handleApiError(error, options);
  }
};

/**
 * 安全执行函数
 * @param {function} fn 要执行的函数
 * @param {any} defaultValue 默认返回值
 * @param {object} options 选项
 */
const safeExecute = async (fn, defaultValue = null, options = {}) => {
  try {
    return await fn();
  } catch (error) {
    handleError(error, 'GENERAL', options);
    return defaultValue;
  }
};

module.exports = {
  ErrorHandler,
  errorHandler,
  handleError,
  safeExecute
};
