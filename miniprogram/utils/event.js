// utils/event.js

/**
 * 事件管理器
 * 用于页面间通信和状态同步
 */
class EventManager {
  constructor() {
    this.events = {};
  }

  /**
   * 注册事件监听器
   * @param {string} eventName 事件名称
   * @param {function} callback 回调函数
   * @param {object} context 上下文对象
   */
  on(eventName, callback, context = null) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }

    this.events[eventName].push({
      callback,
      context,
      once: false
    });
  }

  /**
   * 注册一次性事件监听器
   * @param {string} eventName 事件名称
   * @param {function} callback 回调函数
   * @param {object} context 上下文对象
   */
  once(eventName, callback, context = null) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }

    this.events[eventName].push({
      callback,
      context,
      once: true
    });
  }

  /**
   * 移除事件监听器
   * @param {string} eventName 事件名称
   * @param {function} callback 回调函数
   * @param {object} context 上下文对象
   */
  off(eventName, callback = null, context = null) {
    if (!this.events[eventName]) {
      return;
    }

    if (!callback) {
      // 移除所有监听器
      delete this.events[eventName];
      return;
    }

    this.events[eventName] = this.events[eventName].filter(listener => {
      if (callback && listener.callback !== callback) {
        return true;
      }
      if (context && listener.context !== context) {
        return true;
      }
      return false;
    });

    if (this.events[eventName].length === 0) {
      delete this.events[eventName];
    }
  }

  /**
   * 触发事件
   * @param {string} eventName 事件名称
   * @param {any} data 事件数据
   */
  emit(eventName, data = null) {
    if (!this.events[eventName]) {
      return;
    }

    const listeners = [...this.events[eventName]];
    
    listeners.forEach(listener => {
      try {
        if (listener.context) {
          listener.callback.call(listener.context, data);
        } else {
          listener.callback(data);
        }
      } catch (error) {
        console.error(`Event callback error for ${eventName}:`, error);
      }
    });

    // 移除一次性监听器
    this.events[eventName] = this.events[eventName].filter(listener => !listener.once);
    
    if (this.events[eventName].length === 0) {
      delete this.events[eventName];
    }
  }

  /**
   * 清除所有事件监听器
   */
  clear() {
    this.events = {};
  }

  /**
   * 获取事件监听器数量
   * @param {string} eventName 事件名称
   * @returns {number} 监听器数量
   */
  listenerCount(eventName) {
    return this.events[eventName] ? this.events[eventName].length : 0;
  }

  /**
   * 获取所有事件名称
   * @returns {string[]} 事件名称数组
   */
  eventNames() {
    return Object.keys(this.events);
  }
}

// 创建全局事件管理器实例
const eventManager = new EventManager();

/**
 * 页面生命周期事件混入
 * 在页面的 onLoad 中调用 bindEvents，在 onUnload 中调用 unbindEvents
 */
const pageEventMixin = {
  /**
   * 绑定页面事件
   */
  bindEvents() {
    this._eventListeners = this._eventListeners || [];
  },

  /**
   * 添加事件监听器
   * @param {string} eventName 事件名称
   * @param {function} callback 回调函数
   */
  addEventListener(eventName, callback) {
    this._eventListeners = this._eventListeners || [];
    
    eventManager.on(eventName, callback, this);
    this._eventListeners.push({ eventName, callback });
  },

  /**
   * 添加一次性事件监听器
   * @param {string} eventName 事件名称
   * @param {function} callback 回调函数
   */
  addEventListenerOnce(eventName, callback) {
    this._eventListeners = this._eventListeners || [];
    
    eventManager.once(eventName, callback, this);
    this._eventListeners.push({ eventName, callback });
  },

  /**
   * 触发事件
   * @param {string} eventName 事件名称
   * @param {any} data 事件数据
   */
  emitEvent(eventName, data) {
    eventManager.emit(eventName, data);
  },

  /**
   * 解绑页面事件
   */
  unbindEvents() {
    if (this._eventListeners) {
      this._eventListeners.forEach(({ eventName, callback }) => {
        eventManager.off(eventName, callback, this);
      });
      this._eventListeners = [];
    }
  }
};

/**
 * 常用事件名称
 */
const Events = {
  // 用户相关
  USER_LOGIN: 'user:login',
  USER_LOGOUT: 'user:logout',
  USER_INFO_UPDATE: 'user:info:update',
  
  // 学生档案相关
  PROFILE_CREATE: 'profile:create',
  PROFILE_UPDATE: 'profile:update',
  PROFILE_DELETE: 'profile:delete',
  
  // 推荐相关
  RECOMMENDATION_GENERATE: 'recommendation:generate',
  RECOMMENDATION_UPDATE: 'recommendation:update',
  RECOMMENDATION_UNLOCK: 'recommendation:unlock',
  
  // 职业相关
  CAREER_SELECT: 'career:select',
  CAREER_ANALYSIS: 'career:analysis',
  
  // 模拟相关
  SIMULATION_GENERATE: 'simulation:generate',
  SIMULATION_UPDATE: 'simulation:update',
  SIMULATION_SHARE: 'simulation:share',
  
  // 支付相关
  PAYMENT_START: 'payment:start',
  PAYMENT_SUCCESS: 'payment:success',
  PAYMENT_FAIL: 'payment:fail',
  
  // 系统相关
  NETWORK_STATUS_CHANGE: 'system:network:change',
  APP_SHOW: 'app:show',
  APP_HIDE: 'app:hide',
  
  // 页面相关
  PAGE_REFRESH: 'page:refresh',
  PAGE_BACK: 'page:back'
};

module.exports = {
  eventManager,
  pageEventMixin,
  Events
};
