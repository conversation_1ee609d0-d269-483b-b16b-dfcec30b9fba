/* pages/profile/profile.wxss */
.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 100rpx;
}

/* 未登录状态 */
.login-prompt {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  padding: 40rpx;
}

.prompt-content {
  text-align: center;
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.prompt-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.prompt-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.prompt-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 40rpx;
}

.login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 24rpx 80rpx;
  font-size: 32rpx;
  font-weight: 600;
}

/* 用户信息卡片 */
.user-card {
  background: white;
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.user-phone {
  font-size: 26rpx;
  color: #666;
}

.user-badge {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-top: 2rpx solid #f1f3f4;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  background: white;
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单 */
.menu-section {
  margin: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.menu-list {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f1f3f4;
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #f8f9fa;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 6rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #666;
}

.menu-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 学生档案状态 */
.profile-status {
  background: white;
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.edit-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2rpx solid #667eea;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.status-content {
  margin-bottom: 20rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f1f3f4;
  font-size: 28rpx;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  color: #666;
}

.status-value {
  color: #333;
  font-weight: 600;
}

.status-prediction {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20rpx;
  border-radius: 16rpx;
  text-align: center;
  font-size: 26rpx;
}

/* 空档案提示 */
.empty-profile {
  background: white;
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.4;
}

.create-profile-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 底部版权信息 */
.footer-info {
  text-align: center;
  padding: 40rpx 20rpx;
  color: #999;
}

.copyright {
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.version {
  font-size: 22rpx;
}
