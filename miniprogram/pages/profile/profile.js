// pages/profile/profile.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    studentProfile: null,
    isLoggedIn: false,
    menuItems: [
      {
        icon: '📊',
        title: '我的档案',
        desc: '查看和编辑个人信息',
        action: 'viewProfile'
      },
      {
        icon: '🎯',
        title: '推荐历史',
        desc: '查看历史推荐记录',
        action: 'viewRecommendations'
      },
      {
        icon: '🔮',
        title: '模拟记录',
        desc: '查看AI人生模拟记录',
        action: 'viewSimulations'
      },
      {
        icon: '💰',
        title: '订单记录',
        desc: '查看购买记录',
        action: 'viewOrders'
      },
      {
        icon: '🎁',
        title: '邀请好友',
        desc: '邀请好友获得奖励',
        action: 'inviteFriends'
      },
      {
        icon: '❓',
        title: '帮助中心',
        desc: '常见问题和使用指南',
        action: 'showHelp'
      },
      {
        icon: '⚙️',
        title: '设置',
        desc: '账号设置和隐私设置',
        action: 'showSettings'
      }
    ],
    stats: {
      recommendationCount: 0,
      simulationCount: 0,
      totalSpent: 0
    }
  },

  onLoad() {
    this.checkLoginStatus();
  },

  onShow() {
    this.checkLoginStatus();
    if (this.data.isLoggedIn) {
      this.loadUserStats();
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = app.globalData.userInfo;
    const studentProfile = wx.getStorageSync('student_profile');
    
    this.setData({
      userInfo,
      studentProfile,
      isLoggedIn: !!userInfo
    });
  },

  // 加载用户统计数据
  async loadUserStats() {
    try {
      // 这里可以调用API获取用户统计数据
      // 暂时使用本地存储的数据
      const stats = {
        recommendationCount: wx.getStorageSync('recommendation_count') || 0,
        simulationCount: wx.getStorageSync('simulation_count') || 0,
        totalSpent: wx.getStorageSync('total_spent') || 0
      };
      
      this.setData({ stats });
    } catch (error) {
      console.error('加载用户统计失败:', error);
    }
  },

  // 登录
  login() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 处理菜单点击
  handleMenuTap(e) {
    const { action } = e.currentTarget.dataset;
    
    if (!this.data.isLoggedIn) {
      this.login();
      return;
    }

    switch (action) {
      case 'viewProfile':
        this.viewProfile();
        break;
      case 'viewRecommendations':
        this.viewRecommendations();
        break;
      case 'viewSimulations':
        this.viewSimulations();
        break;
      case 'viewOrders':
        this.viewOrders();
        break;
      case 'inviteFriends':
        this.inviteFriends();
        break;
      case 'showHelp':
        this.showHelp();
        break;
      case 'showSettings':
        this.showSettings();
        break;
    }
  },

  // 查看档案
  viewProfile() {
    if (this.data.studentProfile) {
      wx.navigateTo({
        url: '/pages/form/form'
      });
    } else {
      wx.showModal({
        title: '提示',
        content: '您还没有创建学生档案，是否立即创建？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/form/form'
            });
          }
        }
      });
    }
  },

  // 查看推荐历史
  viewRecommendations() {
    wx.switchTab({
      url: '/pages/results/results'
    });
  },

  // 查看模拟记录
  viewSimulations() {
    wx.switchTab({
      url: '/pages/simulation/simulation'
    });
  },

  // 查看订单记录
  viewOrders() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 邀请好友
  inviteFriends() {
    const userInfo = this.data.userInfo;
    if (!userInfo) return;

    wx.showActionSheet({
      itemList: ['生成邀请码', '分享给朋友'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.generateInviteCode();
        } else if (res.tapIndex === 1) {
          this.shareToFriend();
        }
      }
    });
  },

  // 生成邀请码
  generateInviteCode() {
    // 生成邀请码逻辑
    const inviteCode = 'INVITE' + Date.now().toString().slice(-6);
    
    wx.showModal({
      title: '我的邀请码',
      content: `您的邀请码是：${inviteCode}\n\n邀请好友注册可获得奖励！`,
      confirmText: '复制邀请码',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: inviteCode,
            success: () => {
              app.showSuccess('邀请码已复制');
            }
          });
        }
      }
    });
  },

  // 分享给朋友
  shareToFriend() {
    // 触发分享
    wx.showShareMenu({
      withShareTicket: true
    });
  },

  // 显示帮助
  showHelp() {
    wx.showActionSheet({
      itemList: ['使用指南', '常见问题', '联系客服'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.showGuide();
        } else if (res.tapIndex === 1) {
          this.showFAQ();
        } else if (res.tapIndex === 2) {
          this.contactService();
        }
      }
    });
  },

  // 显示使用指南
  showGuide() {
    wx.showModal({
      title: '使用指南',
      content: '1. 完善个人信息\n2. 获取AI推荐\n3. 选择职业方向\n4. 查看人生模拟\n5. 获取详细报告',
      showCancel: false
    });
  },

  // 显示常见问题
  showFAQ() {
    wx.showModal({
      title: '常见问题',
      content: 'Q: 推荐结果准确吗？\nA: 基于大数据和AI算法，准确率较高\n\nQ: 如何获取完整报告？\nA: 支付相应费用即可解锁',
      showCancel: false
    });
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服微信：zhiyuan-ai\n工作时间：9:00-18:00',
      confirmText: '复制微信号',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: 'zhiyuan-ai',
            success: () => {
              app.showSuccess('微信号已复制');
            }
          });
        }
      }
    });
  },

  // 显示设置
  showSettings() {
    wx.showActionSheet({
      itemList: ['清除缓存', '隐私设置', '关于我们', '退出登录'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.clearCache();
        } else if (res.tapIndex === 1) {
          this.showPrivacySettings();
        } else if (res.tapIndex === 2) {
          this.showAbout();
        } else if (res.tapIndex === 3) {
          this.logout();
        }
      }
    });
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除所有缓存数据吗？这不会影响您的账号信息。',
      success: (res) => {
        if (res.confirm) {
          // 清除非关键缓存
          wx.removeStorageSync('form_draft');
          app.showSuccess('缓存已清除');
        }
      }
    });
  },

  // 隐私设置
  showPrivacySettings() {
    wx.showModal({
      title: '隐私设置',
      content: '我们严格保护您的隐私，不会泄露任何个人信息。',
      showCancel: false
    });
  },

  // 关于我们
  showAbout() {
    wx.showModal({
      title: '关于我们',
      content: 'AI志愿规划师 v1.0\n\n专业的高考志愿填报指导平台\n让每个选择都有科学依据',
      showCancel: false
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.clearLoginStatus();
          this.setData({
            userInfo: null,
            studentProfile: null,
            isLoggedIn: false
          });
          app.showSuccess('已退出登录');
        }
      }
    });
  },

  // 分享给朋友
  onShareAppMessage() {
    return {
      title: 'AI志愿规划师 - 科学规划你的人生',
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-cover.png'
    };
  }
});
