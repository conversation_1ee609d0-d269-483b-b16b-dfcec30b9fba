<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 未登录状态 -->
  <view class="login-prompt" wx:if="{{!isLoggedIn}}">
    <view class="prompt-content">
      <view class="prompt-icon">👤</view>
      <view class="prompt-title">登录后查看更多功能</view>
      <view class="prompt-desc">登录后可以保存推荐记录、查看模拟历史等</view>
      <button class="login-btn" bindtap="login">立即登录</button>
    </view>
  </view>

  <!-- 已登录状态 -->
  <view class="profile-content" wx:else>
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-header">
        <view class="user-avatar">
          <image src="{{userInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill" />
        </view>
        <view class="user-info">
          <view class="user-name">{{userInfo.nickname || '用户'}}</view>
          <view class="user-phone">{{userInfo.phone || '未绑定手机'}}</view>
        </view>
        <view class="user-badge" wx:if="{{studentProfile}}">
          <text class="badge-text">已认证</text>
        </view>
      </view>

      <!-- 用户统计 -->
      <view class="user-stats">
        <view class="stat-item">
          <view class="stat-number">{{stats.recommendationCount}}</view>
          <view class="stat-label">推荐次数</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{stats.simulationCount}}</view>
          <view class="stat-label">模拟次数</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">¥{{stats.totalSpent}}</view>
          <view class="stat-label">累计消费</view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="action-item" bindtap="viewProfile">
        <view class="action-icon">📝</view>
        <view class="action-text">完善档案</view>
      </view>
      <view class="action-item" bindtap="viewRecommendations">
        <view class="action-icon">🎯</view>
        <view class="action-text">查看推荐</view>
      </view>
      <view class="action-item" bindtap="viewSimulations">
        <view class="action-icon">🔮</view>
        <view class="action-text">人生模拟</view>
      </view>
      <view class="action-item" bindtap="inviteFriends">
        <view class="action-icon">🎁</view>
        <view class="action-text">邀请好友</view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="section-title">功能菜单</view>
      <view class="menu-list">
        <view 
          class="menu-item"
          wx:for="{{menuItems}}" 
          wx:key="action"
          data-action="{{item.action}}"
          bindtap="handleMenuTap"
        >
          <view class="menu-icon">{{item.icon}}</view>
          <view class="menu-content">
            <view class="menu-title">{{item.title}}</view>
            <view class="menu-desc">{{item.desc}}</view>
          </view>
          <view class="menu-arrow">></view>
        </view>
      </view>
    </view>

    <!-- 学生档案状态 -->
    <view class="profile-status" wx:if="{{studentProfile}}">
      <view class="status-header">
        <view class="status-title">📊 我的档案</view>
        <button class="edit-btn" bindtap="viewProfile">编辑</button>
      </view>
      <view class="status-content">
        <view class="status-item">
          <text class="status-label">姓名：</text>
          <text class="status-value">{{studentProfile.name}}</text>
        </view>
        <view class="status-item">
          <text class="status-label">分数：</text>
          <text class="status-value">{{studentProfile.score}}分</text>
        </view>
        <view class="status-item">
          <text class="status-label">省份：</text>
          <text class="status-value">{{studentProfile.province}}</text>
        </view>
        <view class="status-item">
          <text class="status-label">选科：</text>
          <text class="status-value">{{studentProfile.subject_combination}}</text>
        </view>
      </view>
      <view class="status-prediction" wx:if="{{studentProfile.predicted_rank}}">
        <text class="prediction-text">
          🎖️ 预估位次：全省前{{studentProfile.predicted_rank.province_rank}}
        </text>
      </view>
    </view>

    <!-- 空档案提示 -->
    <view class="empty-profile" wx:else>
      <view class="empty-icon">📋</view>
      <view class="empty-text">还没有创建学生档案</view>
      <view class="empty-desc">完善档案信息，获取精准推荐</view>
      <button class="create-profile-btn" bindtap="viewProfile">创建档案</button>
    </view>
  </view>

  <!-- 底部版权信息 -->
  <view class="footer-info">
    <view class="copyright">© 2025 AI志愿规划师</view>
    <view class="version">版本 1.0.0</view>
  </view>
</view>
