// pages/career/career.js
const app = getApp();

Page({
  data: {
    selectedRecommendation: null,
    careers: [],
    selectedCareer: null,
    isLoading: true,
    hasError: false,
    matchAnalysis: null
  },

  onLoad() {
    this.loadSelectedRecommendation();
    this.loadCareers();
  },

  // 加载选中的推荐
  loadSelectedRecommendation() {
    const recommendation = app.globalData.selectedRecommendation;
    if (recommendation) {
      this.setData({ selectedRecommendation: recommendation });
    } else {
      // 没有选中的推荐，返回推荐页
      wx.navigateBack();
    }
  },

  // 加载职业列表
  async loadCareers() {
    const { selectedRecommendation } = this.data;
    if (!selectedRecommendation) return;

    try {
      this.setData({ isLoading: true, hasError: false });

      const result = await app.request({
        url: `/career/by-major/${selectedRecommendation.major_id}`,
        method: 'GET',
        data: {
          school_tier: this.getSchoolTier(selectedRecommendation.school_name)
        }
      });

      this.setData({
        careers: result.data.careers,
        isLoading: false
      });

    } catch (error) {
      console.error('加载职业列表失败:', error);
      this.setData({ 
        isLoading: false, 
        hasError: true 
      });
    }
  },

  // 获取学校层次
  getSchoolTier(schoolName) {
    // 简单的学校层次判断逻辑，实际应该从数据库获取
    const tier985 = ['清华大学', '北京大学', '华中科技大学', '武汉大学', '中南大学'];
    const tier211 = ['华中师范大学', '中南财经政法大学', '华中农业大学'];
    
    if (tier985.some(name => schoolName.includes(name))) {
      return '985';
    } else if (tier211.some(name => schoolName.includes(name))) {
      return '211';
    } else {
      return 'tier1';
    }
  },

  // 选择职业
  async selectCareer(e) {
    const { index } = e.currentTarget.dataset;
    const career = this.data.careers[index];
    
    this.setData({ selectedCareer: career });

    // 获取匹配度分析
    await this.getMatchAnalysis(career);
  },

  // 获取匹配度分析
  async getMatchAnalysis(career) {
    const studentProfile = wx.getStorageSync('student_profile');
    if (!studentProfile) return;

    try {
      app.showLoading('分析中...');

      const result = await app.request({
        url: '/career/match-analysis',
        method: 'POST',
        data: {
          profile_id: studentProfile.profile_id,
          career_id: career.career_id,
          school_tier: this.getSchoolTier(this.data.selectedRecommendation.school_name)
        }
      });

      app.hideLoading();
      
      this.setData({
        matchAnalysis: result.data
      });

    } catch (error) {
      app.hideLoading();
      console.error('获取匹配度分析失败:', error);
    }
  },

  // 开始AI人生模拟
  startSimulation() {
    const { selectedCareer, selectedRecommendation } = this.data;
    
    if (!selectedCareer) {
      app.showError('请先选择一个职业方向');
      return;
    }

    // 保存选择的职业到全局数据
    app.globalData.selectedCareer = selectedCareer;
    app.globalData.selectedRecommendation = selectedRecommendation;

    wx.navigateTo({
      url: '/pages/simulation/simulation'
    });
  },

  // 查看职业详情
  viewCareerDetail(e) {
    const { index } = e.currentTarget.dataset;
    const career = this.data.careers[index];
    
    wx.showModal({
      title: career.career_name,
      content: `${career.job_description}\n\n发展路径：${career.development_path}\n\n行业前景：${career.industry_outlook}`,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 重新加载
  reload() {
    this.loadCareers();
  },

  // 返回推荐页
  goBack() {
    wx.navigateBack();
  }
});
