/* pages/career/career.wxss */
.container {
  min-height: 100vh;
  background: #f7f8fa;
}

/* 头部信息 */
.header-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
  color: white;
}

.selected-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.school-major {
  flex: 1;
}

.school-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.major-name {
  font-size: 26rpx;
  opacity: 0.9;
}

.probability {
  background: rgba(255,255,255,0.2);
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  backdrop-filter: blur(10px);
}

/* 加载和错误状态 */
.loading, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.loading-icon, .error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.loading-text, .error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.retry-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 职业选择内容 */
.career-content {
  padding: 40rpx 20rpx;
}

.section-title {
  text-align: center;
  margin-bottom: 40rpx;
}

.title-main {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.title-sub {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

/* 职业列表 */
.career-list {
  margin-bottom: 40rpx;
}

.career-item {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  position: relative;
  border: 4rpx solid transparent;
}

.career-item:active {
  transform: translateY(4rpx);
}

.career-item.selected {
  border-color: #667eea;
  background: #f8f9ff;
}

.career-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.career-title {
  display: flex;
  align-items: center;
}

.career-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.career-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.career-category {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.career-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
}

.info-item {
  font-size: 26rpx;
}

.info-label {
  color: #666;
}

.info-value {
  color: #333;
  font-weight: 600;
}

.info-value.salary {
  color: #e74c3c;
}

.info-value.probability {
  color: #27ae60;
}

.career-desc {
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.career-requirements {
  margin-bottom: 20rpx;
}

.req-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.req-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.req-tag {
  background: #f1f3f4;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
}

.career-actions {
  text-align: right;
}

.detail-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2rpx solid #667eea;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.selected-mark {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

/* 匹配度分析 */
.match-analysis {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.analysis-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.match-score {
  text-align: center;
  margin-bottom: 30rpx;
}

.score-circle {
  display: inline-block;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
}

.score-number {
  font-size: 36rpx;
  font-weight: bold;
}

.score-label {
  font-size: 20rpx;
}

.analysis-details {
  margin-bottom: 30rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f1f3f4;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  width: 120rpx;
}

.detail-score {
  font-size: 28rpx;
  color: #667eea;
  font-weight: bold;
  width: 80rpx;
  text-align: center;
}

.detail-desc {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-left: 20rpx;
}

.recommendations, .risk-factors {
  margin-bottom: 20rpx;
}

.rec-title, .risk-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.rec-title {
  color: #27ae60;
}

.risk-title {
  color: #e74c3c;
}

.rec-list, .risk-list {
  padding-left: 20rpx;
}

.rec-item, .risk-item {
  font-size: 26rpx;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.rec-item {
  color: #666;
}

.risk-item {
  color: #e74c3c;
}

/* 开始模拟 */
.simulation-section {
  text-align: center;
}

.simulation-btn {
  width: 100%;
  padding: 30rpx;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 36rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.simulation-btn:active {
  transform: translateY(4rpx);
}

.simulation-btn.disabled {
  background: #ccc;
  color: #999;
}

.simulation-tip {
  font-size: 26rpx;
  color: #999;
  margin-top: 20rpx;
}
