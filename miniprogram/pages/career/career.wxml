<!--pages/career/career.wxml-->
<view class="container">
  <!-- 头部信息 -->
  <view class="header-info" wx:if="{{selectedRecommendation}}">
    <view class="selected-info">
      <view class="school-major">
        <view class="school-name">{{selectedRecommendation.school_name}}</view>
        <view class="major-name">{{selectedRecommendation.major_name}}</view>
      </view>
      <view class="probability">录取概率 {{selectedRecommendation.admission_probability}}%</view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{isLoading}}">
    <view class="loading-icon">💼</view>
    <view class="loading-text">正在加载职业信息...</view>
  </view>

  <!-- 错误状态 -->
  <view class="error-state" wx:elif="{{hasError}}">
    <view class="error-icon">😔</view>
    <view class="error-text">加载失败，请重试</view>
    <button class="retry-btn" bindtap="reload">重新加载</button>
  </view>

  <!-- 职业选择内容 -->
  <view class="career-content" wx:else>
    <view class="section-title">
      <view class="title-main">选择你的理想职业</view>
      <view class="title-sub">基于推荐专业，选择一个最感兴趣的职业方向</view>
    </view>

    <!-- 职业列表 -->
    <view class="career-list">
      <view 
        class="career-item {{selectedCareer && selectedCareer.career_id === item.career_id ? 'selected' : ''}}"
        wx:for="{{careers}}" 
        wx:key="career_id"
        wx:for-index="index"
        data-index="{{index}}"
        bindtap="selectCareer"
      >
        <view class="career-header">
          <view class="career-title">
            <view class="career-icon">💻</view>
            <view class="career-name">{{item.career_name}}</view>
          </view>
          <view class="career-category">{{item.career_category}}</view>
        </view>

        <view class="career-info">
          <view class="info-row">
            <view class="info-item">
              <text class="info-label">起薪：</text>
              <text class="info-value salary">{{item.salary_info.entry_level}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">入职概率：</text>
              <text class="info-value probability">{{item.entry_probability['985'] || item.entry_probability['211'] || '中等'}}</text>
            </view>
          </view>
        </view>

        <view class="career-desc">{{item.job_description}}</view>

        <view class="career-requirements">
          <view class="req-title">技能要求：</view>
          <view class="req-tags">
            <text class="req-tag" wx:for="{{item.requirements.skills}}" wx:key="*this">{{item}}</text>
          </view>
        </view>

        <!-- 详情按钮 -->
        <view class="career-actions">
          <button 
            class="detail-btn" 
            data-index="{{index}}"
            bindtap="viewCareerDetail"
            catchtap="viewCareerDetail"
          >
            查看详情
          </button>
        </view>

        <!-- 选中标识 -->
        <view class="selected-mark" wx:if="{{selectedCareer && selectedCareer.career_id === item.career_id}}">
          ✓
        </view>
      </view>
    </view>

    <!-- 匹配度分析 -->
    <view class="match-analysis" wx:if="{{matchAnalysis}}">
      <view class="analysis-title">🎯 匹配度分析</view>
      <view class="match-score">
        <view class="score-circle">
          <view class="score-number">{{matchAnalysis.match_score}}</view>
          <view class="score-label">匹配度</view>
        </view>
      </view>

      <view class="analysis-details">
        <view class="detail-item">
          <view class="detail-label">能力匹配</view>
          <view class="detail-score">{{matchAnalysis.analysis.ability_match.score}}分</view>
          <view class="detail-desc">{{matchAnalysis.analysis.ability_match.details}}</view>
        </view>
        <view class="detail-item">
          <view class="detail-label">兴趣匹配</view>
          <view class="detail-score">{{matchAnalysis.analysis.interest_match.score}}分</view>
          <view class="detail-desc">{{matchAnalysis.analysis.interest_match.details}}</view>
        </view>
        <view class="detail-item">
          <view class="detail-label">背景匹配</view>
          <view class="detail-score">{{matchAnalysis.analysis.background_match.score}}分</view>
          <view class="detail-desc">{{matchAnalysis.analysis.background_match.details}}</view>
        </view>
      </view>

      <view class="recommendations" wx:if="{{matchAnalysis.recommendations.length > 0}}">
        <view class="rec-title">💡 建议</view>
        <view class="rec-list">
          <view class="rec-item" wx:for="{{matchAnalysis.recommendations}}" wx:key="*this">
            • {{item}}
          </view>
        </view>
      </view>

      <view class="risk-factors" wx:if="{{matchAnalysis.risk_factors.length > 0}}">
        <view class="risk-title">⚠️ 风险提示</view>
        <view class="risk-list">
          <view class="risk-item" wx:for="{{matchAnalysis.risk_factors}}" wx:key="*this">
            • {{item}}
          </view>
        </view>
      </view>
    </view>

    <!-- 开始模拟按钮 -->
    <view class="simulation-section">
      <button 
        class="simulation-btn {{selectedCareer ? '' : 'disabled'}}" 
        bindtap="startSimulation"
        disabled="{{!selectedCareer}}"
      >
        🔮 开始AI人生模拟
      </button>
      <view class="simulation-tip" wx:if="{{!selectedCareer}}">
        请先选择一个职业方向
      </view>
    </view>
  </view>
</view>
