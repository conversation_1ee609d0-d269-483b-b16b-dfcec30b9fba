/* pages/form/form.wxss */
.container {
  min-height: 100vh;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
}

/* 头部进度 */
.header {
  background: white;
  padding: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.progress-info {
  text-align: center;
}

.step-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.progress-bar {
  height: 8rpx;
  background: #e1e8ed;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 表单内容 */
.form-content {
  flex: 1;
  padding: 40rpx;
}

.step-content {
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.step-title {
  text-align: center;
  margin-bottom: 60rpx;
}

.title-main {
  font-size: 44rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.title-sub {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-weight: 600;
  color: #333;
  font-size: 28rpx;
}

.required {
  color: #e74c3c;
}

.form-input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 4rpx solid #e1e8ed;
  border-radius: 16rpx;
  font-size: 32rpx;
  background: white;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
}

.form-select {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 4rpx solid #e1e8ed;
  border-radius: 16rpx;
  font-size: 32rpx;
  background: white;
  box-sizing: border-box;
  color: #333;
  position: relative;
}

.form-select::after {
  content: '>';
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%) rotate(90deg);
  color: #999;
  font-size: 24rpx;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.checkbox-item {
  padding: 16rpx 32rpx;
  border: 4rpx solid #e1e8ed;
  border-radius: 40rpx;
  background: white;
  font-size: 28rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.checkbox-item:active {
  transform: scale(0.95);
}

.checkbox-item.selected {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* 完成提示 */
.completion-tip {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-top: 40rpx;
}

.tip-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.tip-text {
  flex: 1;
}

.tip-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.tip-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
}

/* 底部按钮 */
.bottom-actions {
  display: flex;
  gap: 20rpx;
  padding: 40rpx;
  background: white;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
}

.btn {
  padding: 30rpx 40rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
}

.btn:active {
  transform: translateY(4rpx);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-secondary {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2rpx solid #667eea;
}

.btn.loading {
  opacity: 0.7;
}

.flex-1 {
  flex: 1;
}

/* 保存草稿 */
.save-draft {
  position: fixed;
  top: 50%;
  right: 20rpx;
  transform: translateY(-50%);
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 20rpx;
  border-radius: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  backdrop-filter: blur(10px);
  z-index: 100;
}

.save-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.save-text {
  font-size: 20rpx;
  writing-mode: vertical-lr;
}
