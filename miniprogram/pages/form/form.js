// pages/form/form.js
const app = getApp();

Page({
  data: {
    // 表单数据
    formData: {
      name: '',
      score: '',
      province: '',
      subject_combination: '',
      exam_type: 'new_gaokao',
      preferred_regions: [],
      major_preferences: [],
      family_economic_status: 'middle_class',
      special_requirements: {
        physical_condition: 'normal',
        family_expectations: '',
        personal_interests: []
      }
    },
    
    // 选项数据
    provinces: [
      { code: 'hubei', name: '湖北' },
      { code: 'henan', name: '河南' },
      { code: 'shandong', name: '山东' },
      { code: 'guangdong', name: '广东' },
      { code: 'jiangsu', name: '江苏' },
      { code: 'zhejiang', name: '浙江' },
      { code: 'beijing', name: '北京' },
      { code: 'shanghai', name: '上海' }
    ],
    
    subjectCombinations: [
      { code: 'physics_chemistry_biology', name: '物理+化学+生物' },
      { code: 'physics_chemistry_geography', name: '物理+化学+地理' },
      { code: 'physics_biology_geography', name: '物理+生物+地理' },
      { code: 'history_politics_geography', name: '历史+政治+地理' },
      { code: 'history_politics_biology', name: '历史+政治+生物' },
      { code: 'history_geography_biology', name: '历史+地理+生物' }
    ],
    
    regionOptions: [
      { code: 'local', name: '本省' },
      { code: 'tier1_cities', name: '北上广深' },
      { code: 'new_tier1', name: '新一线城市' },
      { code: 'tier2_cities', name: '二线城市' },
      { code: 'unlimited', name: '不限' }
    ],
    
    majorOptions: [
      { code: 'engineering', name: '理工类' },
      { code: 'economics', name: '经管类' },
      { code: 'liberal_arts', name: '文史类' },
      { code: 'medicine', name: '医学类' },
      { code: 'arts', name: '艺术类' },
      { code: 'education', name: '教育类' },
      { code: 'law', name: '法学类' },
      { code: 'agriculture', name: '农学类' }
    ],
    
    interestOptions: [
      { code: 'technology', name: '科技创新' },
      { code: 'business', name: '商业管理' },
      { code: 'art_design', name: '艺术设计' },
      { code: 'social_service', name: '社会服务' },
      { code: 'research', name: '学术研究' },
      { code: 'education', name: '教育培训' }
    ],
    
    // UI状态
    currentStep: 1,
    totalSteps: 3,
    isSubmitting: false,
    progress: 33,

    // 计算属性
    selectedProvinceName: '',
    selectedSubjectName: ''
  },

  onLoad() {
    // 检查是否已有档案数据
    const savedProfile = wx.getStorageSync('student_profile');
    if (savedProfile) {
      this.setData({
        formData: { ...this.data.formData, ...savedProfile }
      });

      // 更新选中的名称
      this.updateSelectedNames();
    }
  },

  // 更新选中的名称
  updateSelectedNames() {
    const { formData, provinces, subjectCombinations } = this.data;

    // 更新省份名称
    if (formData.province) {
      const province = provinces.find(p => p.code === formData.province);
      if (province) {
        this.setData({ selectedProvinceName: province.name });
      }
    }

    // 更新选科组合名称
    if (formData.subject_combination) {
      const subject = subjectCombinations.find(s => s.code === formData.subject_combination);
      if (subject) {
        this.setData({ selectedSubjectName: subject.name });
      }
    }
  },

  // 输入姓名
  onNameInput(e) {
    this.setData({
      'formData.name': e.detail.value
    });
  },

  // 输入分数
  onScoreInput(e) {
    this.setData({
      'formData.score': e.detail.value
    });
  },

  // 选择省份
  onProvinceChange(e) {
    const index = e.detail.value;
    const province = this.data.provinces[index];
    this.setData({
      'formData.province': province.code,
      selectedProvinceName: province.name
    });
  },

  // 选择选科组合
  onSubjectChange(e) {
    const index = e.detail.value;
    const subject = this.data.subjectCombinations[index];
    this.setData({
      'formData.subject_combination': subject.code,
      selectedSubjectName: subject.name
    });
  },

  // 切换地区偏好
  toggleRegion(e) {
    const { code } = e.currentTarget.dataset;
    const regions = [...this.data.formData.preferred_regions];
    const index = regions.indexOf(code);
    
    if (index > -1) {
      regions.splice(index, 1);
    } else {
      regions.push(code);
    }
    
    this.setData({
      'formData.preferred_regions': regions
    });
  },

  // 切换专业偏好
  toggleMajor(e) {
    const { code } = e.currentTarget.dataset;
    const majors = [...this.data.formData.major_preferences];
    const index = majors.indexOf(code);
    
    if (index > -1) {
      majors.splice(index, 1);
    } else {
      majors.push(code);
    }
    
    this.setData({
      'formData.major_preferences': majors
    });
  },

  // 切换兴趣爱好
  toggleInterest(e) {
    const { code } = e.currentTarget.dataset;
    const interests = [...this.data.formData.special_requirements.personal_interests];
    const index = interests.indexOf(code);
    
    if (index > -1) {
      interests.splice(index, 1);
    } else {
      interests.push(code);
    }
    
    this.setData({
      'formData.special_requirements.personal_interests': interests
    });
  },

  // 下一步
  nextStep() {
    if (!this.validateCurrentStep()) {
      return;
    }
    
    const nextStep = this.data.currentStep + 1;
    const progress = (nextStep / this.data.totalSteps) * 100;
    
    this.setData({
      currentStep: nextStep,
      progress
    });
  },

  // 上一步
  prevStep() {
    const prevStep = this.data.currentStep - 1;
    const progress = (prevStep / this.data.totalSteps) * 100;
    
    this.setData({
      currentStep: prevStep,
      progress
    });
  },

  // 验证当前步骤
  validateCurrentStep() {
    const { currentStep, formData } = this.data;
    
    switch (currentStep) {
      case 1:
        if (!formData.name.trim()) {
          app.showError('请输入姓名');
          return false;
        }
        if (!formData.score || formData.score < 100 || formData.score > 750) {
          app.showError('请输入有效的高考分数(100-750)');
          return false;
        }
        if (!formData.province) {
          app.showError('请选择所在省份');
          return false;
        }
        if (!formData.subject_combination) {
          app.showError('请选择选科组合');
          return false;
        }
        break;
      case 2:
        // 第二步为可选项，不做强制验证
        break;
      case 3:
        // 第三步为可选项，不做强制验证
        break;
    }
    
    return true;
  },

  // 提交表单
  async submitForm() {
    if (!this.validateCurrentStep()) {
      return;
    }
    
    if (this.data.isSubmitting) return;
    
    try {
      this.setData({ isSubmitting: true });
      app.showLoading('提交中...');
      
      const result = await app.request({
        url: '/student/profile',
        method: 'POST',
        data: this.data.formData
      });
      
      app.hideLoading();
      
      // 保存档案数据
      const profileData = {
        ...this.data.formData,
        profile_id: result.data.profile_id,
        predicted_rank: result.data.predicted_rank,
        school_tier_prediction: result.data.school_tier_prediction
      };
      
      wx.setStorageSync('student_profile', profileData);
      app.globalData.studentProfile = profileData;
      
      app.showSuccess('信息提交成功');
      
      // 跳转到推荐结果页
      setTimeout(() => {
        wx.redirectTo({
          url: '/pages/results/results'
        });
      }, 1000);
      
    } catch (error) {
      app.hideLoading();
      this.setData({ isSubmitting: false });
      console.error('提交失败:', error);
    }
  },

  // 保存草稿
  saveDraft() {
    wx.setStorageSync('form_draft', this.data.formData);
    app.showSuccess('已保存草稿');
  }
});
