<!--pages/form/form.wxml-->
<view class="container">
  <!-- 头部进度 -->
  <view class="header">
    <view class="progress-info">
      <view class="step-text">第{{currentStep}}步，共{{totalSteps}}步</view>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{progress}}%"></view>
      </view>
    </view>
  </view>

  <!-- 表单内容 -->
  <view class="form-content">
    <!-- 第一步：基本信息 -->
    <view class="step-content" wx:if="{{currentStep === 1}}">
      <view class="step-title">
        <view class="title-main">告诉我你的基本情况</view>
        <view class="title-sub">这些信息将帮助我们为你提供精准推荐</view>
      </view>

      <view class="form-group">
        <view class="form-label">姓名 <text class="required">*</text></view>
        <input 
          class="form-input" 
          placeholder="请输入你的姓名" 
          value="{{formData.name}}"
          bindinput="onNameInput"
        />
      </view>

      <view class="form-group">
        <view class="form-label">高考分数 <text class="required">*</text></view>
        <input 
          class="form-input" 
          type="number" 
          placeholder="请输入你的高考分数" 
          value="{{formData.score}}"
          bindinput="onScoreInput"
        />
      </view>

      <view class="form-group">
        <view class="form-label">所在省份 <text class="required">*</text></view>
        <picker
          mode="selector"
          range="{{provinces}}"
          range-key="name"
          bindchange="onProvinceChange"
        >
          <view class="form-select">
            {{selectedProvinceName || '请选择省份'}}
          </view>
        </picker>
      </view>

      <view class="form-group">
        <view class="form-label">选科组合 <text class="required">*</text></view>
        <picker
          mode="selector"
          range="{{subjectCombinations}}"
          range-key="name"
          bindchange="onSubjectChange"
        >
          <view class="form-select">
            {{selectedSubjectName || '请选择选科组合'}}
          </view>
        </picker>
      </view>
    </view>

    <!-- 第二步：偏好设置 -->
    <view class="step-content" wx:if="{{currentStep === 2}}">
      <view class="step-title">
        <view class="title-main">你的偏好是什么？</view>
        <view class="title-sub">这些信息将帮助我们更好地匹配你的需求</view>
      </view>

      <view class="form-group">
        <view class="form-label">意向地区（可多选）</view>
        <view class="checkbox-group">
          <view 
            class="checkbox-item {{formData.preferred_regions.includes(item.code) ? 'selected' : ''}}"
            wx:for="{{regionOptions}}" 
            wx:key="code"
            data-code="{{item.code}}"
            bindtap="toggleRegion"
          >
            {{item.name}}
          </view>
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">专业倾向（可多选）</view>
        <view class="checkbox-group">
          <view 
            class="checkbox-item {{formData.major_preferences.includes(item.code) ? 'selected' : ''}}"
            wx:for="{{majorOptions}}" 
            wx:key="code"
            data-code="{{item.code}}"
            bindtap="toggleMajor"
          >
            {{item.name}}
          </view>
        </view>
      </view>
    </view>

    <!-- 第三步：个人特质 -->
    <view class="step-content" wx:if="{{currentStep === 3}}">
      <view class="step-title">
        <view class="title-main">最后，了解一下你的兴趣</view>
        <view class="title-sub">这将帮助我们为你规划更合适的职业发展路径</view>
      </view>

      <view class="form-group">
        <view class="form-label">兴趣爱好（可多选）</view>
        <view class="checkbox-group">
          <view 
            class="checkbox-item {{formData.special_requirements.personal_interests.includes(item.code) ? 'selected' : ''}}"
            wx:for="{{interestOptions}}" 
            wx:key="code"
            data-code="{{item.code}}"
            bindtap="toggleInterest"
          >
            {{item.name}}
          </view>
        </view>
      </view>

      <view class="completion-tip">
        <view class="tip-icon">🎉</view>
        <view class="tip-text">
          <view class="tip-title">即将完成</view>
          <view class="tip-desc">点击提交后，AI将为你生成专属的志愿推荐方案</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button 
      class="btn btn-secondary" 
      wx:if="{{currentStep > 1}}"
      bindtap="prevStep"
    >
      上一步
    </button>
    
    <button 
      class="btn btn-primary flex-1" 
      wx:if="{{currentStep < totalSteps}}"
      bindtap="nextStep"
    >
      下一步
    </button>
    
    <button 
      class="btn btn-primary flex-1 {{isSubmitting ? 'loading' : ''}}" 
      wx:if="{{currentStep === totalSteps}}"
      bindtap="submitForm"
      disabled="{{isSubmitting}}"
    >
      {{isSubmitting ? '提交中...' : '生成推荐方案'}}
    </button>
  </view>

  <!-- 保存草稿按钮 -->
  <view class="save-draft" bindtap="saveDraft">
    <text class="save-icon">💾</text>
    <text class="save-text">保存草稿</text>
  </view>
</view>
