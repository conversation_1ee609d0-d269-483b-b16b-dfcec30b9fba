<!--pages/results/results.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading" wx:if="{{isLoading}}">
    <view class="loading-icon">🤖</view>
    <view class="loading-text">AI正在为你生成推荐方案...</view>
  </view>

  <!-- 错误状态 -->
  <view class="error-state" wx:elif="{{hasError}}">
    <view class="error-icon">😔</view>
    <view class="error-text">生成推荐失败，请重试</view>
    <button class="retry-btn" bindtap="regenerateRecommendations">重新生成</button>
  </view>

  <!-- 推荐结果 -->
  <view class="results-content" wx:else>
    <!-- 学生信息卡片 -->
    <view class="student-card" wx:if="{{studentProfile}}">
      <view class="card-header">
        <view class="student-name">{{studentProfile.name}}</view>
        <button class="edit-btn" bindtap="editProfile">修改</button>
      </view>
      <view class="student-info">
        <view class="info-item">
          <text class="info-label">分数：</text>
          <text class="info-value">{{studentProfile.score}}分</text>
        </view>
        <view class="info-item">
          <text class="info-label">省份：</text>
          <text class="info-value">{{studentProfile.province}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">选科：</text>
          <text class="info-value">{{studentProfile.subject_combination}}</text>
        </view>
      </view>
      <view class="prediction-info" wx:if="{{studentProfile.predicted_rank}}">
        <text class="prediction-text">
          🎖️ 预估位次：全省前{{studentProfile.predicted_rank.province_rank}} | 
          💡 推荐策略：冲稳保结合
        </text>
      </view>
    </view>

    <!-- 推荐统计 -->
    <view class="summary-card" wx:if="{{summary}}">
      <view class="summary-title">为你推荐 {{summary.total_count}} 个志愿</view>
      <view class="summary-stats">
        <view class="stat-item">
          <view class="stat-number">{{summary.rush_count}}</view>
          <view class="stat-label">冲刺</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{summary.stable_count}}</view>
          <view class="stat-label">稳妥</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{summary.safe_count}}</view>
          <view class="stat-label">保底</view>
        </view>
      </view>
    </view>

    <!-- 标签页 -->
    <view class="tabs">
      <view 
        class="tab-item {{currentTab === item.key ? 'active' : ''}}"
        wx:for="{{tabs}}" 
        wx:key="key"
        data-tab="{{item.key}}"
        bindtap="switchTab"
      >
        <text class="tab-name">{{item.name}}</text>
        <text class="tab-count" wx:if="{{item.count > 0}}">({{item.count}})</text>
      </view>
    </view>

    <!-- 推荐列表 -->
    <view class="recommendation-list">
      <view
        class="recommendation-item"
        wx:for="{{filteredRecommendations}}"
        wx:key="school_id"
        wx:for-index="index"
      >
        <!-- 推荐类型标签 -->
        <view class="type-tag {{item.recommendation_type}}">
          {{item.recommendation_type === 'rush' ? '冲刺' : item.recommendation_type === 'stable' ? '稳妥' : '保底'}}
        </view>

        <!-- 学校和专业信息 -->
        <view class="school-info">
          <view class="school-name">{{item.school_name}}</view>
          <view class="major-name">{{item.major_name}}</view>
        </view>

        <!-- 录取概率 -->
        <view class="probability-section">
          <view class="probability-label">录取概率</view>
          <view class="probability-value {{item.admission_probability >= 80 ? 'high' : item.admission_probability >= 60 ? 'medium' : 'low'}}">
            {{item.admission_probability}}%
          </view>
        </view>

        <!-- 分数分析 -->
        <view class="score-analysis" wx:if="{{item.score_analysis}}">
          <view class="score-item">
            <text class="score-label">录取线：</text>
            <text class="score-value">{{item.score_analysis.required_score}}分</text>
          </view>
          <view class="score-item">
            <text class="score-label">你的分数：</text>
            <text class="score-value">{{item.score_analysis.student_score}}分</text>
          </view>
          <view class="score-item">
            <text class="score-label">分数优势：</text>
            <text class="score-value advantage">+{{item.score_analysis.score_advantage}}分</text>
          </view>
        </view>

        <!-- 推荐理由预览 -->
        <view class="preview-reason">
          {{item.preview_reason}}
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <button 
            class="action-btn detail-btn"
            data-index="{{index}}"
            bindtap="unlockAnalysis"
          >
            {{item.is_unlocked ? '查看详情' : '详细分析 ¥' + item.unlock_price}}
          </button>
          <button 
            class="action-btn career-btn"
            data-index="{{index}}"
            bindtap="selectForCareer"
          >
            职业规划
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{showEmptyState}}">
      <view class="empty-icon">📚</view>
      <view class="empty-text">暂无推荐结果</view>
      <button class="retry-btn" bindtap="regenerateRecommendations">重新生成</button>
    </view>
  </view>
</view>
