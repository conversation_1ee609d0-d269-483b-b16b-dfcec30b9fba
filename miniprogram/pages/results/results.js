// pages/results/results.js
const app = getApp();

Page({
  data: {
    studentProfile: null,
    recommendations: [],
    summary: null,
    isLoading: true,
    hasError: false,
    currentTab: 'all', // all, rush, stable, safe
    tabs: [
      { key: 'all', name: '全部', count: 0 },
      { key: 'rush', name: '冲刺', count: 0 },
      { key: 'stable', name: '稳妥', count: 0 },
      { key: 'safe', name: '保底', count: 0 }
    ]
  },

  onLoad() {
    this.loadStudentProfile();
    this.generateRecommendations();
  },

  onShow() {
    // 检查是否有新的推荐数据
    const currentRecommendation = app.globalData.currentRecommendation;
    if (currentRecommendation && currentRecommendation.recommendations) {
      this.setData({
        recommendations: currentRecommendation.recommendations,
        summary: currentRecommendation.summary,
        isLoading: false
      });
      this.updateTabCounts();
    }
  },

  // 加载学生档案
  loadStudentProfile() {
    const profile = wx.getStorageSync('student_profile') || app.globalData.studentProfile;
    if (profile) {
      this.setData({ studentProfile: profile });
    } else {
      // 没有档案，跳转到信息收集页
      wx.redirectTo({
        url: '/pages/form/form'
      });
    }
  },

  // 生成推荐
  async generateRecommendations() {
    const { studentProfile } = this.data;
    if (!studentProfile || !studentProfile.profile_id) {
      this.setData({ 
        isLoading: false, 
        hasError: true 
      });
      return;
    }

    try {
      this.setData({ isLoading: true, hasError: false });

      const result = await app.request({
        url: '/recommendation/generate',
        method: 'POST',
        data: {
          profile_id: studentProfile.profile_id,
          recommendation_type: 'comprehensive',
          max_recommendations: 10
        }
      });

      const { recommendations, summary } = result.data;
      
      this.setData({
        recommendations,
        summary,
        isLoading: false
      });

      // 保存到全局数据
      app.globalData.currentRecommendation = result.data;
      
      // 更新标签页计数
      this.updateTabCounts();

    } catch (error) {
      console.error('生成推荐失败:', error);
      this.setData({ 
        isLoading: false, 
        hasError: true 
      });
    }
  },

  // 更新标签页计数
  updateTabCounts() {
    const { recommendations } = this.data;
    const tabs = this.data.tabs.map(tab => {
      let count = 0;
      if (tab.key === 'all') {
        count = recommendations.length;
      } else {
        count = recommendations.filter(item => item.recommendation_type === tab.key).length;
      }
      return { ...tab, count };
    });
    
    this.setData({ tabs });
  },

  // 切换标签页
  switchTab(e) {
    const { tab } = e.currentTarget.dataset;
    this.setData({ currentTab: tab });
  },

  // 获取过滤后的推荐列表
  getFilteredRecommendations() {
    const { recommendations, currentTab } = this.data;
    if (currentTab === 'all') {
      return recommendations;
    }
    return recommendations.filter(item => item.recommendation_type === currentTab);
  },

  // 解锁详细分析
  async unlockAnalysis(e) {
    const { index } = e.currentTarget.dataset;
    const filteredRecommendations = this.getFilteredRecommendations();
    const recommendation = filteredRecommendations[index];

    if (recommendation.is_unlocked) {
      // 已解锁，直接查看详情
      this.viewDetail(recommendation);
      return;
    }

    // 显示支付确认
    const result = await this.showPaymentConfirm(recommendation);
    if (!result.confirm) return;

    try {
      app.showLoading('处理中...');

      // 创建订单
      const orderResult = await app.request({
        url: '/payment/create-order',
        method: 'POST',
        data: {
          product_type: 'detailed_analysis',
          product_ids: [`${recommendation.school_id}_${recommendation.major_id}`],
          amount: recommendation.unlock_price,
          payment_method: 'wechat_pay'
        }
      });

      // 调用微信支付
      await this.requestPayment(orderResult.data.payment_info);

      // 支付成功，解锁详细分析
      const unlockResult = await app.request({
        url: '/recommendation/unlock',
        method: 'POST',
        data: {
          recommendation_id: app.globalData.currentRecommendation.recommendation_id,
          item_ids: [`${recommendation.school_id}_${recommendation.major_id}`],
          payment_token: orderResult.data.order_id
        }
      });

      app.hideLoading();
      app.showSuccess('解锁成功');

      // 更新推荐数据
      const updatedRecommendations = [...this.data.recommendations];
      const originalIndex = updatedRecommendations.findIndex(
        item => item.school_id === recommendation.school_id && item.major_id === recommendation.major_id
      );
      
      if (originalIndex > -1) {
        updatedRecommendations[originalIndex] = {
          ...updatedRecommendations[originalIndex],
          is_unlocked: true,
          detailed_analysis: unlockResult.data.detailed_analysis
        };
        
        this.setData({ recommendations: updatedRecommendations });
      }

      // 查看详情
      this.viewDetail({
        ...recommendation,
        is_unlocked: true,
        detailed_analysis: unlockResult.data.detailed_analysis
      });

    } catch (error) {
      app.hideLoading();
      console.error('解锁失败:', error);
    }
  },

  // 显示支付确认
  showPaymentConfirm(recommendation) {
    return new Promise((resolve) => {
      wx.showModal({
        title: '解锁详细分析',
        content: `解锁《${recommendation.school_name} - ${recommendation.major_name}》的详细分析报告，包含专业分析、就业前景、匹配度等信息。\n\n费用：¥${recommendation.unlock_price}`,
        confirmText: '立即支付',
        cancelText: '取消',
        success: resolve
      });
    });
  },

  // 调用微信支付
  requestPayment(paymentInfo) {
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        timeStamp: paymentInfo.timestamp,
        nonceStr: paymentInfo.nonce_str,
        package: `prepay_id=${paymentInfo.prepay_id}`,
        signType: 'MD5',
        paySign: paymentInfo.sign,
        success: resolve,
        fail: reject
      });
    });
  },

  // 查看详情
  viewDetail(recommendation) {
    // 将详细数据存储到全局，供详情页使用
    app.globalData.currentRecommendationDetail = recommendation;
    
    wx.navigateTo({
      url: '/pages/detail/detail'
    });
  },

  // 选择职业规划
  selectForCareer(e) {
    const { index } = e.currentTarget.dataset;
    const filteredRecommendations = this.getFilteredRecommendations();
    const recommendation = filteredRecommendations[index];

    // 保存选择的推荐到全局数据
    app.globalData.selectedRecommendation = recommendation;

    wx.navigateTo({
      url: '/pages/career/career'
    });
  },

  // 重新生成推荐
  regenerateRecommendations() {
    this.generateRecommendations();
  },

  // 修改信息
  editProfile() {
    wx.navigateTo({
      url: '/pages/form/form'
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.generateRecommendations().finally(() => {
      wx.stopPullDownRefresh();
    });
  }
});
