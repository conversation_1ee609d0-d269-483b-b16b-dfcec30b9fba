/* pages/results/results.wxss */
.container {
  min-height: 100vh;
  background: #f7f8fa;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.loading-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 40rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.retry-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 学生信息卡片 */
.student-card {
  background: white;
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.student-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.edit-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2rpx solid #667eea;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.student-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.info-item {
  font-size: 28rpx;
}

.info-label {
  color: #666;
}

.info-value {
  color: #333;
  font-weight: 600;
}

.prediction-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
}

/* 推荐统计 */
.summary-card {
  background: white;
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  text-align: center;
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 标签页 */
.tabs {
  display: flex;
  background: white;
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 10rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.tab-name {
  font-weight: 600;
}

.tab-count {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 推荐列表 */
.recommendation-list {
  padding: 0 20rpx 20rpx;
}

.recommendation-item {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  position: relative;
}

.type-tag {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  color: white;
}

.type-tag.rush {
  background: #e74c3c;
}

.type-tag.stable {
  background: #f39c12;
}

.type-tag.safe {
  background: #27ae60;
}

.school-info {
  margin-bottom: 20rpx;
  padding-right: 80rpx;
}

.school-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.major-name {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
}

.probability-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.probability-label {
  font-size: 28rpx;
  color: #666;
}

.probability-value {
  font-size: 32rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.probability-value.high {
  background: #d4edda;
  color: #155724;
}

.probability-value.medium {
  background: #fff3cd;
  color: #856404;
}

.probability-value.low {
  background: #f8d7da;
  color: #721c24;
}

.score-analysis {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.score-item {
  text-align: center;
  font-size: 24rpx;
}

.score-label {
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.score-value {
  color: #333;
  font-weight: 600;
  font-size: 28rpx;
}

.score-value.advantage {
  color: #27ae60;
}

.preview-reason {
  color: #666;
  line-height: 1.5;
  margin-bottom: 30rpx;
  font-size: 28rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
}

.detail-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.career-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2rpx solid #667eea;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #666;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 40rpx;
}
