<!--pages/home/<USER>
<view class="container">
  <!-- 头部区域 -->
  <view class="home-header">
    <view class="home-title">AI志愿规划师</view>
    <view class="home-subtitle">不只告诉你选什么，更告诉你为什么选</view>
    <button class="start-btn" bindtap="startPlanning">
      {{hasProfile ? '查看我的推荐' : '开始规划人生'}}
    </button>
  </view>

  <!-- 功能特色 -->
  <view class="features">
    <view class="section-title">产品特色</view>
    <view class="feature-list">
      <view 
        class="feature-item" 
        wx:for="{{features}}" 
        wx:key="title"
        data-index="{{index}}"
        bindtap="viewFeature"
      >
        <view class="feature-icon">{{item.icon}}</view>
        <view class="feature-content">
          <view class="feature-title">{{item.title}}</view>
          <view class="feature-desc">{{item.desc}}</view>
        </view>
        <view class="feature-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 用户状态提示 -->
  <view class="user-status" wx:if="{{userInfo}}">
    <view class="status-card">
      <view class="status-header">
        <view class="user-avatar">
          <image src="{{userInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill" />
        </view>
        <view class="user-info">
          <view class="user-name">{{userInfo.nickname || '用户'}}</view>
          <view class="user-desc">{{hasProfile ? '已完成信息收集' : '还未完成信息收集'}}</view>
        </view>
      </view>
      
      <view class="status-actions">
        <button class="action-btn" bindtap="startPlanning">
          {{hasProfile ? '查看推荐' : '完善信息'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 底部说明 -->
  <view class="bottom-info">
    <view class="info-item">
      <view class="info-icon">🔒</view>
      <view class="info-text">数据安全保护</view>
    </view>
    <view class="info-item">
      <view class="info-icon">🎓</view>
      <view class="info-text">专业教育团队</view>
    </view>
    <view class="info-item">
      <view class="info-icon">🤖</view>
      <view class="info-text">AI智能分析</view>
    </view>
  </view>
</view>
