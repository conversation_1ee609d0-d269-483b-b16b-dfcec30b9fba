// pages/home/<USER>
const app = getApp();

Page({
  data: {
    userInfo: null,
    hasProfile: false,
    features: [
      {
        icon: '🎯',
        title: '精准推荐',
        desc: '基于分数和兴趣的个性化院校推荐'
      },
      {
        icon: '💡',
        title: '详细解释',
        desc: '每个推荐都有专业理由，让你心里有底'
      },
      {
        icon: '🚀',
        title: '职业规划',
        desc: '从专业选择到职业发展的全链条规划'
      },
      {
        icon: '🔮',
        title: '人生模拟',
        desc: 'AI预测你的职业发展轨迹和人生路径'
      }
    ]
  },

  onLoad() {
    this.checkUserStatus();
  },

  onShow() {
    this.checkUserStatus();
  },

  // 检查用户状态
  checkUserStatus() {
    const userInfo = app.globalData.userInfo;
    const studentProfile = wx.getStorageSync('student_profile');
    
    this.setData({
      userInfo,
      hasProfile: !!studentProfile
    });
  },

  // 开始规划
  startPlanning() {
    if (!app.globalData.accessToken) {
      // 未登录，跳转到登录页
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return;
    }

    if (this.data.hasProfile) {
      // 已有档案，直接跳转到推荐结果页
      wx.switchTab({
        url: '/pages/results/results'
      });
    } else {
      // 没有档案，跳转到信息收集页
      wx.navigateTo({
        url: '/pages/form/form'
      });
    }
  },

  // 查看功能详情
  viewFeature(e) {
    const { index } = e.currentTarget.dataset;
    const feature = this.data.features[index];
    
    wx.showModal({
      title: feature.title,
      content: feature.desc,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 分享给朋友
  onShareAppMessage() {
    return {
      title: 'AI志愿规划师 - 不只告诉你选什么，更告诉你为什么选',
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-cover.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: 'AI志愿规划师 - 科学规划你的人生',
      imageUrl: '/images/share-cover.png'
    };
  }
});
