/* pages/home/<USER>/
.container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 头部区域 */
.home-header {
  padding: 80rpx 40rpx 60rpx;
  text-align: center;
  color: white;
}

.home-title {
  font-size: 56rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.home-subtitle {
  font-size: 32rpx;
  opacity: 0.9;
  margin-bottom: 60rpx;
  line-height: 1.4;
}

.start-btn {
  background: rgba(255,255,255,0.2);
  border: 4rpx solid rgba(255,255,255,0.3);
  color: white;
  padding: 30rpx 80rpx;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.start-btn:active {
  background: rgba(255,255,255,0.3);
  transform: translateY(4rpx);
}

/* 功能特色 */
.features {
  background: #f7f8fa;
  border-radius: 40rpx 40rpx 0 0;
  padding: 60rpx 40rpx;
  margin-top: 40rpx;
}

.section-title {
  font-size: 40rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
}

.feature-item:active {
  transform: translateY(4rpx);
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.feature-icon {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  margin-right: 30rpx;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.feature-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.feature-arrow {
  font-size: 32rpx;
  color: #ccc;
  margin-left: 20rpx;
}

/* 用户状态 */
.user-status {
  padding: 0 40rpx 40rpx;
  background: #f7f8fa;
}

.status-card {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 26rpx;
  color: #666;
}

.status-actions {
  text-align: center;
}

.action-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 底部说明 */
.bottom-info {
  display: flex;
  justify-content: space-around;
  padding: 40rpx;
  background: #f7f8fa;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.info-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.info-text {
  font-size: 24rpx;
  color: #666;
}
