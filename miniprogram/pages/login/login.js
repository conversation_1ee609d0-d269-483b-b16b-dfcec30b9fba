// pages/login/login.js
const app = getApp();

Page({
  data: {
    phone: '',
    verificationCode: '',
    countdown: 0,
    isLoading: false,
    canSendCode: true,
    inviteCode: ''
  },

  onLoad(options) {
    // 获取邀请码
    if (options.invite) {
      this.setData({
        inviteCode: options.invite
      });
    }
  },

  // 输入手机号
  onPhoneInput(e) {
    this.setData({
      phone: e.detail.value
    });
  },

  // 输入验证码
  onCodeInput(e) {
    this.setData({
      verificationCode: e.detail.value
    });
  },

  // 输入邀请码
  onInviteCodeInput(e) {
    this.setData({
      inviteCode: e.detail.value
    });
  },

  // 发送验证码
  async sendVerificationCode() {
    const { phone, canSendCode } = this.data;
    
    if (!canSendCode) return;
    
    if (!this.validatePhone(phone)) {
      app.showError('请输入正确的手机号');
      return;
    }

    try {
      app.showLoading('发送中...');
      
      await app.request({
        url: '/auth/send-code',
        method: 'POST',
        data: {
          phone,
          type: 'login'
        },
        needAuth: false
      });

      app.hideLoading();
      app.showSuccess('验证码已发送');
      
      // 开始倒计时
      this.startCountdown();
      
    } catch (error) {
      app.hideLoading();
      console.error('发送验证码失败:', error);
    }
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60;
    this.setData({
      countdown,
      canSendCode: false
    });

    const timer = setInterval(() => {
      countdown--;
      this.setData({ countdown });
      
      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({
          canSendCode: true,
          countdown: 0
        });
      }
    }, 1000);
  },

  // 登录
  async login() {
    const { phone, verificationCode, inviteCode, isLoading } = this.data;
    
    if (isLoading) return;
    
    if (!this.validatePhone(phone)) {
      app.showError('请输入正确的手机号');
      return;
    }
    
    if (!verificationCode) {
      app.showError('请输入验证码');
      return;
    }

    try {
      this.setData({ isLoading: true });
      app.showLoading('登录中...');
      
      const result = await app.request({
        url: '/auth/login',
        method: 'POST',
        data: {
          phone,
          verification_code: verificationCode,
          invite_code: inviteCode || undefined
        },
        needAuth: false
      });

      app.hideLoading();
      
      // 保存登录状态
      app.saveLoginStatus(result.data);
      
      app.showSuccess('登录成功');
      
      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        // 检查是否有学生档案
        const studentProfile = wx.getStorageSync('student_profile');
        if (studentProfile) {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        } else {
          wx.navigateTo({
            url: '/pages/form/form'
          });
        }
      }, 1000);
      
    } catch (error) {
      app.hideLoading();
      this.setData({ isLoading: false });
      console.error('登录失败:', error);
    }
  },

  // 验证手机号
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  // 微信授权登录
  async wechatLogin() {
    try {
      app.showLoading('授权中...');
      
      // 获取微信登录凭证
      const loginRes = await wx.login();
      
      if (!loginRes.code) {
        throw new Error('获取微信登录凭证失败');
      }

      // 发送到后端验证
      const result = await app.request({
        url: '/auth/wechat-login',
        method: 'POST',
        data: {
          code: loginRes.code,
          invite_code: this.data.inviteCode || undefined
        },
        needAuth: false
      });

      app.hideLoading();
      
      // 保存登录状态
      app.saveLoginStatus(result.data);
      
      app.showSuccess('登录成功');
      
      setTimeout(() => {
        const studentProfile = wx.getStorageSync('student_profile');
        if (studentProfile) {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        } else {
          wx.navigateTo({
            url: '/pages/form/form'
          });
        }
      }, 1000);
      
    } catch (error) {
      app.hideLoading();
      console.error('微信登录失败:', error);
    }
  },

  // 返回首页
  goBack() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  }
});
