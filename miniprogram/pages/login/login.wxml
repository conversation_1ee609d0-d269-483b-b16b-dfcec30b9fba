<!--pages/login/login.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="back-btn" bindtap="goBack">
      <text class="back-icon">‹</text>
    </view>
    <view class="header-title">登录/注册</view>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <view class="form-title">
      <view class="title-main">欢迎使用</view>
      <view class="title-sub">AI志愿规划师</view>
    </view>

    <!-- 手机号输入 -->
    <view class="form-group">
      <view class="form-label">手机号</view>
      <input style="height: 87rpx; display: block; box-sizing: border-box" 
        class="form-input" 
        type="number" 
        placeholder="请输入手机号" 
        value="{{phone}}"
        bindinput="onPhoneInput"
        maxlength="11"
      />
    </view>

    <!-- 验证码输入 -->
    <view class="form-group">
      <view class="form-label">验证码</view>
      <view class="code-input-group">
        <input style="height: 83rpx; display: block; box-sizing: border-box" 
          class="form-input code-input" 
          type="number" 
          placeholder="请输入验证码" 
          value="{{verificationCode}}"
          bindinput="onCodeInput"
          maxlength="6"
        />
        <button 
          class="send-code-btn {{canSendCode ? 'active' : 'disabled'}}" 
          bindtap="sendVerificationCode"
          disabled="{{!canSendCode}}"
        >
          {{countdown > 0 ? countdown + 's' : '发送验证码'}}
        </button>
      </view>
    </view>

    <!-- 邀请码输入（可选） -->
    <view class="form-group">
      <view class="form-label">邀请码（可选）</view>
      <input style="height: 85rpx; display: block; box-sizing: border-box" 
        class="form-input" 
        type="text" 
        placeholder="请输入邀请码" 
        value="{{inviteCode}}"
        bindinput="onInviteCodeInput"
      />
    </view>

    <!-- 登录按钮 -->
    <button 
      class="login-btn {{isLoading ? 'loading' : ''}}" 
      bindtap="login"
      disabled="{{isLoading}}"
    >
      {{isLoading ? '登录中...' : '登录/注册'}}
    </button>

    <!-- 分割线 -->
    <view class="divider">
      <view class="divider-line"></view>
      <view class="divider-text">或</view>
      <view class="divider-line"></view>
    </view>

    <!-- 微信登录 -->
    <button class="wechat-login-btn" bindtap="wechatLogin">
      <view class="wechat-icon">🔗</view>
      <view class="wechat-text">微信快速登录</view>
    </button>
  </view>

  <!-- 用户协议 -->
  <view class="agreement">
    <view class="agreement-text">
      登录即表示同意
      <text class="agreement-link">《用户协议》</text>
      和
      <text class="agreement-link">《隐私政策》</text>
    </view>
  </view>

  <!-- 底部装饰 -->
  <view class="bottom-decoration">
    <view class="decoration-circle circle-1"></view>
    <view class="decoration-circle circle-2"></view>
    <view class="decoration-circle circle-3"></view>
  </view>
</view>
