/* pages/login/login.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 头部 */
.header {
  display: flex;
  align-items: center;
  padding: 20rpx 40rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  position: relative;
  z-index: 10;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255,255,255,0.2);
  border-radius: 30rpx;
  backdrop-filter: blur(10px);
}

.back-icon {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
}

.header-title {
  flex: 1;
  text-align: center;
  color: white;
  font-size: 36rpx;
  font-weight: 600;
  margin-right: 60rpx;
}

/* 登录表单 */
.login-form {
  background: white;
  border-radius: 40rpx 40rpx 0 0;
  padding: 60rpx 40rpx;
  margin-top: 80rpx;
  position: relative;
  z-index: 5;
}

.form-title {
  text-align: center;
  margin-bottom: 60rpx;
}

.title-main {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.title-sub {
  font-size: 32rpx;
  color: #667eea;
  font-weight: 600;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 600;
}

.form-input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 4rpx solid #e1e8ed;
  border-radius: 16rpx;
  font-size: 32rpx;
  background: #f8f9fa;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
}

.code-input-group {
  display: flex;
  gap: 20rpx;
}

.code-input {
  flex: 1;
}

.send-code-btn {
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  white-space: nowrap;
}

.send-code-btn.active {
  background: #667eea;
  color: white;
}

.send-code-btn.disabled {
  background: #e1e8ed;
  color: #999;
}

.login-btn {
  width: 100%;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 36rpx;
  font-weight: bold;
  margin-top: 40rpx;
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(4rpx);
}

.login-btn.loading {
  opacity: 0.7;
}

/* 分割线 */
.divider {
  display: flex;
  align-items: center;
  margin: 60rpx 0 40rpx;
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background: #e1e8ed;
}

.divider-text {
  padding: 0 30rpx;
  color: #999;
  font-size: 28rpx;
}

/* 微信登录 */
.wechat-login-btn {
  width: 100%;
  padding: 24rpx;
  background: #07c160;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.wechat-icon {
  font-size: 36rpx;
}

/* 用户协议 */
.agreement {
  text-align: center;
  padding: 40rpx 20rpx;
  background: white;
}

.agreement-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

.agreement-link {
  color: #667eea;
  text-decoration: underline;
}

/* 底部装饰 */
.bottom-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255,255,255,0.1);
}

.circle-1 {
  width: 120rpx;
  height: 120rpx;
  bottom: 50rpx;
  left: 60rpx;
}

.circle-2 {
  width: 80rpx;
  height: 80rpx;
  bottom: 120rpx;
  right: 100rpx;
}

.circle-3 {
  width: 60rpx;
  height: 60rpx;
  bottom: 80rpx;
  right: 200rpx;
}
