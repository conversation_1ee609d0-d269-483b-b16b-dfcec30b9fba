/* pages/simulation/simulation.wxss */
.container {
  min-height: 100vh;
  background: #f7f8fa;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  text-align: center;
  padding: 40rpx;
}

.loading-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

.loading-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.loading-progress {
  width: 100%;
  max-width: 400rpx;
}

.progress-bar {
  height: 8rpx;
  background: #e1e8ed;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  animation: progress 3s infinite;
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

.progress-text {
  font-size: 26rpx;
  color: #999;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 40rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.retry-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 头部信息 */
.header-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
  color: white;
  text-align: center;
}

.simulation-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.simulation-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 30rpx;
  line-height: 1.4;
}

.selection-info {
  background: rgba(255,255,255,0.1);
  padding: 20rpx;
  border-radius: 16rpx;
  backdrop-filter: blur(10px);
}

.selection-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
  font-size: 26rpx;
}

.selection-item:last-child {
  margin-bottom: 0;
}

.selection-label {
  opacity: 0.8;
}

.selection-value {
  font-weight: 600;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

/* 时间轴 */
.timeline-section {
  padding: 40rpx 20rpx;
}

.timeline {
  position: relative;
  padding-left: 60rpx;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 40rpx;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.timeline-item {
  position: relative;
  margin-bottom: 60rpx;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -80rpx;
  top: 0;
  width: 36rpx;
  height: 36rpx;
  background: #667eea;
  border-radius: 50%;
  border: 6rpx solid white;
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.1);
}

.timeline-content {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  position: relative;
}

.timeline-content::before {
  content: '';
  position: absolute;
  left: -20rpx;
  top: 30rpx;
  width: 0;
  height: 0;
  border-top: 20rpx solid transparent;
  border-bottom: 20rpx solid transparent;
  border-right: 20rpx solid white;
}

.timeline-year {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.timeline-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.timeline-desc {
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.timeline-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.metric-item {
  font-size: 24rpx;
}

.metric-label {
  color: #666;
}

.metric-value {
  color: #333;
  font-weight: 600;
}

.metric-value.salary {
  color: #e74c3c;
}

.probability-badge {
  display: inline-block;
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.alternatives {
  border-top: 2rpx solid #f1f3f4;
  padding-top: 20rpx;
}

.alternatives-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 600;
}

.alternative-item {
  margin-bottom: 12rpx;
  padding: 16rpx;
  background: #f8f9ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.alt-path {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 600;
  margin-right: 12rpx;
}

.alt-probability {
  font-size: 22rpx;
  color: #999;
  margin-right: 12rpx;
}

.alt-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-top: 8rpx;
}

/* 总结报告 */
.summary-section {
  padding: 0 20rpx 40rpx;
}

.summary-card {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.summary-content {
  margin-bottom: 30rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f1f3f4;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-size: 28rpx;
  color: #666;
}

.summary-value {
  font-size: 32rpx;
  font-weight: bold;
}

.summary-value.income {
  color: #e74c3c;
}

.summary-value.success {
  color: #27ae60;
}

.summary-value.rating {
  color: #f39c12;
}

.factors-section {
  margin-bottom: 30rpx;
}

.factors-section:last-child {
  margin-bottom: 0;
}

.factors-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #27ae60;
}

.factors-title.risk {
  color: #e74c3c;
}

.factors-list {
  padding-left: 20rpx;
}

.factor-item {
  font-size: 26rpx;
  line-height: 1.5;
  margin-bottom: 8rpx;
  color: #666;
}

.factor-item.risk {
  color: #e74c3c;
}

/* 操作按钮 */
.action-section {
  padding: 40rpx 20rpx;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
}

.action-btn.secondary {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2rpx solid #667eea;
}

.full-report-btn {
  width: 100%;
  padding: 30rpx;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.report-tip {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 偏好设置弹窗 */
.preferences-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  position: relative;
  z-index: 1001;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

.preference-group {
  margin-bottom: 40rpx;
}

.preference-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.preference-options {
  display: flex;
  gap: 16rpx;
}

.preference-option {
  flex: 1;
  padding: 20rpx 16rpx;
  border: 2rpx solid #e1e8ed;
  border-radius: 16rpx;
  text-align: center;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.preference-option.selected {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
}

.modal-btn.cancel {
  background: #f1f3f4;
  color: #666;
}

.modal-btn.confirm {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
