<!--pages/simulation/simulation.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading" wx:if="{{isLoading}}">
    <view class="loading-icon">🔮</view>
    <view class="loading-text">AI正在为你模拟人生轨迹...</view>
    <view class="loading-progress">
      <view class="progress-bar">
        <view class="progress-fill"></view>
      </view>
      <view class="progress-text">分析中，请稍候...</view>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-state" wx:elif="{{hasError}}">
    <view class="error-icon">😔</view>
    <view class="error-text">生成模拟失败，请重试</view>
    <button class="retry-btn" bindtap="regenerateSimulation">重新生成</button>
  </view>

  <!-- 模拟结果 -->
  <view class="simulation-content" wx:else>
    <!-- 头部信息 -->
    <view class="header-info">
      <view class="simulation-title">🔮 AI人生模拟报告</view>
      <view class="simulation-subtitle">基于你的选择，AI为你模拟未来15年的发展轨迹</view>
      <view class="selection-info">
        <view class="selection-item">
          <text class="selection-label">院校专业：</text>
          <text class="selection-value">{{selectedRecommendation.school_name}} - {{selectedRecommendation.major_name}}</text>
        </view>
        <view class="selection-item">
          <text class="selection-label">目标职业：</text>
          <text class="selection-value">{{selectedCareer.career_name}}</text>
        </view>
      </view>
    </view>

    <!-- 时间轴 -->
    <view class="timeline-section" wx:if="{{simulation}}">
      <view class="timeline">
        <view 
          class="timeline-item"
          wx:for="{{simulation.timeline}}" 
          wx:key="year_range"
        >
          <view class="timeline-dot"></view>
          <view class="timeline-content">
            <view class="timeline-year">{{item.year_range}} | {{item.stage === 'university' ? '大学阶段' : item.stage === 'early_career' ? '职场初期' : item.stage === 'career_growth' ? '职业发展' : '职业巅峰'}}</view>
            <view class="timeline-title">{{item.title}}</view>
            <view class="timeline-desc">{{item.description}}</view>
            
            <!-- 关键指标 -->
            <view class="timeline-metrics" wx:if="{{item.key_metrics}}">
              <view class="metric-item" wx:if="{{item.key_metrics.salary_range}}">
                <text class="metric-label">年薪：</text>
                <text class="metric-value salary">{{item.key_metrics.salary_range}}</text>
              </view>
              <view class="metric-item" wx:if="{{item.key_metrics.expected_gpa}}">
                <text class="metric-label">预期GPA：</text>
                <text class="metric-value">{{item.key_metrics.expected_gpa}}</text>
              </view>
              <view class="metric-item" wx:if="{{item.key_metrics.company_tier}}">
                <text class="metric-label">公司层次：</text>
                <text class="metric-value">{{item.key_metrics.company_tier}}</text>
              </view>
              <view class="metric-item" wx:if="{{item.key_metrics.team_size}}">
                <text class="metric-label">团队规模：</text>
                <text class="metric-value">{{item.key_metrics.team_size}}</text>
              </view>
            </view>

            <!-- 成功概率 -->
            <view class="probability-badge">
              <text class="probability-text">成功概率 {{item.probability}}%</text>
            </view>

            <!-- 备选路径 -->
            <view class="alternatives" wx:if="{{item.alternatives && item.alternatives.length > 0}}">
              <view class="alternatives-title">备选路径：</view>
              <view class="alternative-item" wx:for="{{item.alternatives}}" wx:key="path" wx:for-item="alt">
                <text class="alt-path">{{alt.path}}</text>
                <text class="alt-probability">({{alt.probability}}%)</text>
                <text class="alt-desc">{{alt.description}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 总结报告 -->
    <view class="summary-section" wx:if="{{simulation && simulation.summary}}">
      <view class="summary-card">
        <view class="summary-title">💰 收入预测总结</view>
        <view class="summary-content">
          <view class="summary-item">
            <text class="summary-label">15年总收入预估：</text>
            <text class="summary-value income">{{simulation.summary.total_income_prediction}}</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">职业发展成功率：</text>
            <text class="summary-value success">{{simulation.summary.career_success_rate}}</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">推荐指数：</text>
            <text class="summary-value rating">{{simulation.summary.recommendation_score}}分</text>
          </view>
        </view>

        <!-- 成功因素 -->
        <view class="factors-section">
          <view class="factors-title">✅ 成功因素</view>
          <view class="factors-list">
            <view class="factor-item" wx:for="{{simulation.summary.success_factors}}" wx:key="*this">
              • {{item}}
            </view>
          </view>
        </view>

        <!-- 风险因素 -->
        <view class="factors-section">
          <view class="factors-title risk">⚠️ 风险因素</view>
          <view class="factors-list">
            <view class="factor-item risk" wx:for="{{simulation.summary.key_risk_factors}}" wx:key="*this">
              • {{item}}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <view class="action-buttons">
        <button class="action-btn secondary" bindtap="showPreferencesModal">
          调整偏好
        </button>
        <button class="action-btn secondary" bindtap="shareSimulation">
          分享结果
        </button>
      </view>
      <button class="full-report-btn" bindtap="getFullReport">
        🎉 获取完整报告 ¥199
      </button>
      <view class="report-tip">
        完整报告包含详细分析、投资建议、风险评估等内容
      </view>
    </view>
  </view>

  <!-- 偏好设置弹窗 -->
  <view class="preferences-modal" wx:if="{{showPreferences}}">
    <view class="modal-mask" bindtap="hidePreferencesModal"></view>
    <view class="modal-content">
      <view class="modal-title">调整模拟偏好</view>
      
      <view class="preference-group">
        <view class="preference-label">风险偏好</view>
        <view class="preference-options">
          <view 
            class="preference-option {{preferences.risk_preference === 'conservative' ? 'selected' : ''}}"
            data-key="risk_preference" 
            data-value="conservative"
            bindtap="updatePreference"
          >
            保守型
          </view>
          <view 
            class="preference-option {{preferences.risk_preference === 'moderate' ? 'selected' : ''}}"
            data-key="risk_preference" 
            data-value="moderate"
            bindtap="updatePreference"
          >
            平衡型
          </view>
          <view 
            class="preference-option {{preferences.risk_preference === 'aggressive' ? 'selected' : ''}}"
            data-key="risk_preference" 
            data-value="aggressive"
            bindtap="updatePreference"
          >
            激进型
          </view>
        </view>
      </view>

      <view class="preference-group">
        <view class="preference-label">职业优先级</view>
        <view class="preference-options">
          <view 
            class="preference-option {{preferences.career_priority === 'salary' ? 'selected' : ''}}"
            data-key="career_priority" 
            data-value="salary"
            bindtap="updatePreference"
          >
            薪资优先
          </view>
          <view 
            class="preference-option {{preferences.career_priority === 'stability' ? 'selected' : ''}}"
            data-key="career_priority" 
            data-value="stability"
            bindtap="updatePreference"
          >
            稳定优先
          </view>
          <view 
            class="preference-option {{preferences.career_priority === 'growth' ? 'selected' : ''}}"
            data-key="career_priority" 
            data-value="growth"
            bindtap="updatePreference"
          >
            发展优先
          </view>
        </view>
      </view>

      <view class="modal-actions">
        <button class="modal-btn cancel" bindtap="hidePreferencesModal">取消</button>
        <button class="modal-btn confirm" bindtap="applyPreferences">应用</button>
      </view>
    </view>
  </view>
</view>
