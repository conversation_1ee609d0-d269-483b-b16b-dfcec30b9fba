// pages/simulation/simulation.js
const app = getApp();

Page({
  data: {
    selectedRecommendation: null,
    selectedCareer: null,
    simulation: null,
    isLoading: true,
    hasError: false,
    isGenerating: false,
    preferences: {
      risk_preference: 'moderate',
      career_priority: 'salary',
      location_preference: 'tier1_cities'
    },
    showPreferences: false
  },

  onLoad() {
    this.loadSelections();
    this.generateSimulation();
  },

  onShow() {
    // 检查是否有新的模拟数据
    const currentSimulation = app.globalData.currentSimulation;
    if (currentSimulation && currentSimulation.timeline) {
      this.setData({
        simulation: currentSimulation,
        isLoading: false
      });
    }
  },

  // 加载选择的数据
  loadSelections() {
    const recommendation = app.globalData.selectedRecommendation;
    const career = app.globalData.selectedCareer;
    
    if (!recommendation || !career) {
      wx.showModal({
        title: '提示',
        content: '请先选择院校专业和职业方向',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    this.setData({
      selectedRecommendation: recommendation,
      selectedCareer: career
    });
  },

  // 生成人生模拟
  async generateSimulation() {
    const { selectedRecommendation, selectedCareer, preferences } = this.data;
    const studentProfile = wx.getStorageSync('student_profile');
    
    if (!selectedRecommendation || !selectedCareer || !studentProfile) {
      this.setData({ 
        isLoading: false, 
        hasError: true 
      });
      return;
    }

    try {
      this.setData({ isLoading: true, hasError: false });

      const result = await app.request({
        url: '/simulation/generate',
        method: 'POST',
        data: {
          profile_id: studentProfile.profile_id,
          selected_school: selectedRecommendation.school_name,
          selected_major: selectedRecommendation.major_name,
          target_career: selectedCareer.career_name,
          simulation_years: 15,
          preferences
        }
      });

      this.setData({
        simulation: result.data,
        isLoading: false
      });

      // 保存到全局数据
      app.globalData.currentSimulation = result.data;

    } catch (error) {
      console.error('生成模拟失败:', error);
      this.setData({ 
        isLoading: false, 
        hasError: true 
      });
    }
  },

  // 显示偏好设置
  showPreferencesModal() {
    this.setData({ showPreferences: true });
  },

  // 隐藏偏好设置
  hidePreferencesModal() {
    this.setData({ showPreferences: false });
  },

  // 更新偏好设置
  updatePreference(e) {
    const { key, value } = e.currentTarget.dataset;
    this.setData({
      [`preferences.${key}`]: value
    });
  },

  // 应用偏好设置
  applyPreferences() {
    this.hidePreferencesModal();
    this.generateSimulation();
  },

  // 重新生成模拟
  regenerateSimulation() {
    this.generateSimulation();
  },

  // 获取完整报告
  async getFullReport() {
    const { simulation } = this.data;
    if (!simulation) return;

    try {
      // 显示支付确认
      const result = await this.showPaymentConfirm();
      if (!result.confirm) return;

      app.showLoading('处理中...');

      // 创建订单
      const orderResult = await app.request({
        url: '/payment/create-order',
        method: 'POST',
        data: {
          product_type: 'simulation_report',
          product_ids: [simulation.simulation_id],
          amount: 199,
          payment_method: 'wechat_pay'
        }
      });

      // 调用微信支付
      await this.requestPayment(orderResult.data.payment_info);

      app.hideLoading();
      app.showSuccess('支付成功');

      // 跳转到完整报告页面或下载报告
      this.downloadReport();

    } catch (error) {
      app.hideLoading();
      console.error('获取完整报告失败:', error);
    }
  },

  // 显示支付确认
  showPaymentConfirm() {
    return new Promise((resolve) => {
      wx.showModal({
        title: '获取完整报告',
        content: '完整的AI人生模拟报告包含详细的发展路径分析、风险评估、投资建议等内容。\n\n费用：¥199',
        confirmText: '立即支付',
        cancelText: '取消',
        success: resolve
      });
    });
  },

  // 调用微信支付
  requestPayment(paymentInfo) {
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        timeStamp: paymentInfo.timestamp,
        nonceStr: paymentInfo.nonce_str,
        package: `prepay_id=${paymentInfo.prepay_id}`,
        signType: 'MD5',
        paySign: paymentInfo.sign,
        success: resolve,
        fail: reject
      });
    });
  },

  // 下载报告
  downloadReport() {
    wx.showModal({
      title: '报告生成中',
      content: '完整报告正在生成，将在24小时内发送到您的微信，请注意查收。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 分享模拟结果
  shareSimulation() {
    // 生成分享图片或链接
    wx.showActionSheet({
      itemList: ['分享给朋友', '保存到相册'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 分享给朋友
          this.onShareAppMessage();
        } else if (res.tapIndex === 1) {
          // 保存到相册
          this.saveToAlbum();
        }
      }
    });
  },

  // 保存到相册
  saveToAlbum() {
    // 生成模拟结果图片并保存
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 重新选择
  reselect() {
    wx.navigateBack();
  },

  // 分享给朋友
  onShareAppMessage() {
    const { selectedRecommendation, selectedCareer } = this.data;
    return {
      title: `我的AI人生模拟：${selectedRecommendation.school_name} ${selectedRecommendation.major_name} → ${selectedCareer.career_name}`,
      path: '/pages/home/<USER>',
      imageUrl: '/images/simulation-share.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: 'AI人生模拟 - 科学规划你的未来',
      imageUrl: '/images/simulation-share.png'
    };
  }
});
