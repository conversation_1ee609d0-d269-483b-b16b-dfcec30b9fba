// app.js
App({
  onLaunch: function () {
    this.globalData = {
      // API配置
      apiBaseUrl: 'https://api.zhiyuan-ai.com/v1',
      userInfo: null,
      accessToken: null,
      studentProfile: null,
      currentRecommendation: null,
      currentSimulation: null,

      // 云开发配置
      env: ""
    };

    // 初始化云开发
    if (!wx.cloud) {
      console.error("请使用 2.2.3 或以上的基础库以使用云能力");
    } else {
      wx.cloud.init({
        env: this.globalData.env,
        traceUser: true,
      });
    }

    // 检查登录状态
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('access_token');
    const userInfo = wx.getStorageSync('user_info');

    if (token && userInfo) {
      this.globalData.accessToken = token;
      this.globalData.userInfo = userInfo;
    }
  },

  // API请求封装
  request(options) {
    const { url, method = 'GET', data = {}, needAuth = true } = options;

    return new Promise((resolve, reject) => {
      const header = {
        'Content-Type': 'application/json'
      };

      // 添加认证头
      if (needAuth && this.globalData.accessToken) {
        header['Authorization'] = `Bearer ${this.globalData.accessToken}`;
      }

      wx.request({
        url: `${this.globalData.apiBaseUrl}${url}`,
        method,
        data,
        header,
        success: (res) => {
          if (res.statusCode === 200) {
            if (res.data.code === 200) {
              resolve(res.data);
            } else {
              // API业务错误
              this.handleApiError(res.data);
              reject(res.data);
            }
          } else if (res.statusCode === 401) {
            // 未授权，清除登录状态
            this.clearLoginStatus();
            wx.navigateTo({
              url: '/pages/login/login'
            });
            reject(res.data);
          } else {
            // HTTP错误
            this.showError('网络请求失败，请稍后重试');
            reject(res.data);
          }
        },
        fail: (err) => {
          console.error('Request failed:', err);
          this.showError('网络连接失败，请检查网络设置');
          reject(err);
        }
      });
    });
  },

  // 处理API错误
  handleApiError(data) {
    const errorMessages = {
      400: '请求参数错误',
      401: '未授权访问',
      403: '禁止访问',
      404: '资源不存在',
      429: '请求过于频繁，请稍后重试',
      500: '服务器内部错误'
    };

    const message = errorMessages[data.code] || data.message || '未知错误';
    this.showError(message);
  },

  // 显示错误信息
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  },

  // 显示成功信息
  showSuccess(message) {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    });
  },

  // 显示加载中
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  // 隐藏加载
  hideLoading() {
    wx.hideLoading();
  },

  // 清除登录状态
  clearLoginStatus() {
    this.globalData.accessToken = null;
    this.globalData.userInfo = null;
    this.globalData.studentProfile = null;
    wx.removeStorageSync('access_token');
    wx.removeStorageSync('user_info');
    wx.removeStorageSync('student_profile');
  },

  // 保存登录状态
  saveLoginStatus(data) {
    this.globalData.accessToken = data.access_token;
    this.globalData.userInfo = data.user_info || {};

    wx.setStorageSync('access_token', data.access_token);
    wx.setStorageSync('user_info', data.user_info || {});
  },

  // 格式化数字
  formatNumber(num) {
    return num < 10 ? '0' + num : num;
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear();
    const month = this.formatNumber(date.getMonth() + 1);
    const day = this.formatNumber(date.getDate());
    return `${year}-${month}-${day}`;
  }
});
