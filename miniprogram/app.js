// app.js
const { errorHandler } = require('./utils/error-handler');
const { eventManager, Events } = require('./utils/event');
const { STORAGE_KEYS, DEFAULT_VALUES } = require('./utils/constants');
const config = require('./utils/config');

App({
  onLaunch: function () {
    this.globalData = {
      // API配置
      apiBaseUrl: config.api.baseUrl,
      userInfo: null,
      accessToken: null,
      studentProfile: null,
      currentRecommendation: null,
      currentSimulation: null,
      selectedRecommendation: null,
      selectedCareer: null,

      // 云开发配置
      env: ""
    };

    // 初始化云开发
    if (!wx.cloud) {
      console.error("请使用 2.2.3 或以上的基础库以使用云能力");
    } else {
      wx.cloud.init({
        env: this.globalData.env,
        traceUser: true,
      });
    }

    // 初始化应用
    this.initApp();
  },

  /**
   * 初始化应用
   */
  initApp() {
    // 检查登录状态
    this.checkLoginStatus();

    // 监听网络状态变化
    this.watchNetworkStatus();

    // 设置全局错误处理
    this.setupGlobalErrorHandler();

    // 加载系统配置
    this.loadSystemConfig();
  },

  /**
   * 监听网络状态变化
   */
  watchNetworkStatus() {
    wx.onNetworkStatusChange((res) => {
      eventManager.emit(Events.NETWORK_STATUS_CHANGE, res);
      if (!res.isConnected) {
        this.showError('网络连接已断开');
      }
    });
  },

  /**
   * 设置全局错误处理
   */
  setupGlobalErrorHandler() {
    // 监听小程序错误
    wx.onError((error) => {
      errorHandler.logError('APP_ERROR', {
        message: error,
        timestamp: new Date().toISOString()
      });
    });

    // 监听未处理的Promise拒绝
    wx.onUnhandledRejection((res) => {
      errorHandler.logError('UNHANDLED_REJECTION', {
        reason: res.reason,
        promise: res.promise,
        timestamp: new Date().toISOString()
      });
    });
  },

  /**
   * 加载系统配置
   */
  async loadSystemConfig() {
    try {
      const systemConfig = wx.getStorageSync(STORAGE_KEYS.SYSTEM_CONFIG);
      if (systemConfig) {
        // 使用缓存的配置
        this.globalData.systemConfig = systemConfig;
      }

      // 异步更新配置
      // const result = await this.request({
      //   url: '/config/system',
      //   method: 'GET',
      //   needAuth: false
      // });
      //
      // if (result.data) {
      //   this.globalData.systemConfig = result.data;
      //   wx.setStorageSync(STORAGE_KEYS.SYSTEM_CONFIG, result.data);
      // }
    } catch (error) {
      console.error('加载系统配置失败:', error);
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const token = wx.getStorageSync(STORAGE_KEYS.ACCESS_TOKEN);
    const userInfo = wx.getStorageSync(STORAGE_KEYS.USER_INFO);
    const studentProfile = wx.getStorageSync(STORAGE_KEYS.STUDENT_PROFILE);

    if (token && userInfo) {
      this.globalData.accessToken = token;
      this.globalData.userInfo = userInfo;
      this.globalData.studentProfile = studentProfile;

      // 触发登录成功事件
      eventManager.emit(Events.USER_LOGIN, { userInfo, studentProfile });
    }
  },

  /**
   * API请求封装
   */
  request(options) {
    const { url, method = 'GET', data = {}, needAuth = true, timeout = config.api.timeout } = options;

    return new Promise((resolve, reject) => {
      const header = {
        'Content-Type': 'application/json'
      };

      // 添加认证头
      if (needAuth && this.globalData.accessToken) {
        header['Authorization'] = `Bearer ${this.globalData.accessToken}`;
      }

      // 添加请求ID用于追踪
      const requestId = this.generateRequestId();
      header['X-Request-ID'] = requestId;

      const requestOptions = {
        url: `${this.globalData.apiBaseUrl}${url}`,
        method,
        data,
        header,
        timeout,
        success: (res) => {
          if (res.statusCode === 200) {
            if (res.data.code === 200) {
              resolve(res.data);
            } else {
              // API业务错误
              const error = {
                ...res.data,
                url,
                method,
                requestId
              };
              errorHandler.handleApiError(error);
              reject(error);
            }
          } else {
            // HTTP错误
            const error = {
              code: res.statusCode,
              message: res.data?.message || '请求失败',
              url,
              method,
              requestId,
              data: res.data
            };
            errorHandler.handleApiError(error);
            reject(error);
          }
        },
        fail: (err) => {
          const error = {
            ...err,
            url,
            method,
            requestId,
            type: 'NETWORK_ERROR'
          };
          errorHandler.handleNetworkError(error);
          reject(error);
        }
      };

      wx.request(requestOptions);
    });
  },

  /**
   * 生成请求ID
   */
  generateRequestId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  },

  /**
   * 显示成功信息
   */
  showSuccess(message) {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    });
  },

  /**
   * 显示加载中
   */
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  /**
   * 隐藏加载
   */
  hideLoading() {
    wx.hideLoading();
  },

  /**
   * 显示确认对话框
   */
  showConfirm(content, title = '提示') {
    return new Promise((resolve) => {
      wx.showModal({
        title,
        content,
        success: (res) => {
          resolve(res.confirm);
        }
      });
    });
  },

  /**
   * 清除登录状态
   */
  clearLoginStatus() {
    // 清除全局数据
    this.globalData.accessToken = null;
    this.globalData.userInfo = null;
    this.globalData.studentProfile = null;
    this.globalData.currentRecommendation = null;
    this.globalData.currentSimulation = null;
    this.globalData.selectedRecommendation = null;
    this.globalData.selectedCareer = null;

    // 清除本地存储
    wx.removeStorageSync(STORAGE_KEYS.ACCESS_TOKEN);
    wx.removeStorageSync(STORAGE_KEYS.REFRESH_TOKEN);
    wx.removeStorageSync(STORAGE_KEYS.USER_INFO);
    wx.removeStorageSync(STORAGE_KEYS.STUDENT_PROFILE);

    // 触发登出事件
    eventManager.emit(Events.USER_LOGOUT);
  },

  /**
   * 保存登录状态
   */
  saveLoginStatus(data) {
    // 保存到全局数据
    this.globalData.accessToken = data.access_token;
    this.globalData.userInfo = data.user_info || {};

    // 保存到本地存储
    wx.setStorageSync(STORAGE_KEYS.ACCESS_TOKEN, data.access_token);
    wx.setStorageSync(STORAGE_KEYS.USER_INFO, data.user_info || {});

    if (data.refresh_token) {
      wx.setStorageSync(STORAGE_KEYS.REFRESH_TOKEN, data.refresh_token);
    }

    // 触发登录成功事件
    eventManager.emit(Events.USER_LOGIN, {
      userInfo: data.user_info,
      accessToken: data.access_token
    });
  },

  /**
   * 更新用户信息
   */
  updateUserInfo(userInfo) {
    this.globalData.userInfo = { ...this.globalData.userInfo, ...userInfo };
    wx.setStorageSync(STORAGE_KEYS.USER_INFO, this.globalData.userInfo);

    // 触发用户信息更新事件
    eventManager.emit(Events.USER_INFO_UPDATE, this.globalData.userInfo);
  },

  /**
   * 更新学生档案
   */
  updateStudentProfile(profile) {
    this.globalData.studentProfile = profile;
    wx.setStorageSync(STORAGE_KEYS.STUDENT_PROFILE, profile);

    // 触发档案更新事件
    eventManager.emit(Events.PROFILE_UPDATE, profile);
  },

  /**
   * 格式化数字
   */
  formatNumber(num) {
    return num < 10 ? '0' + num : num;
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = this.formatNumber(date.getMonth() + 1);
    const day = this.formatNumber(date.getDate());
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化时间
   */
  formatTime(date) {
    const year = date.getFullYear();
    const month = this.formatNumber(date.getMonth() + 1);
    const day = this.formatNumber(date.getDate());
    const hour = this.formatNumber(date.getHours());
    const minute = this.formatNumber(date.getMinutes());
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return this.globalData.userInfo;
  },

  /**
   * 获取学生档案
   */
  getStudentProfile() {
    return this.globalData.studentProfile;
  },

  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    return !!(this.globalData.accessToken && this.globalData.userInfo);
  },

  /**
   * 检查是否有学生档案
   */
  hasStudentProfile() {
    return !!this.globalData.studentProfile;
  },

  /**
   * 应用显示时
   */
  onShow() {
    eventManager.emit(Events.APP_SHOW);
  },

  /**
   * 应用隐藏时
   */
  onHide() {
    eventManager.emit(Events.APP_HIDE);
  },

  /**
   * 应用错误处理
   */
  onError(error) {
    errorHandler.logError('APP_ERROR', {
      message: error,
      timestamp: new Date().toISOString()
    });
  }
});
