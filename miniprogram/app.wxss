/**app.wxss**/
page {
  background-color: #f7f8fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #333;
}

.container {
  min-height: 100vh;
  padding: 0;
  box-sizing: border-box;
}

button {
  background: initial;
}

button:focus{
  outline: 0;
}

button::after{
  border: none;
}

/* 通用样式 */
.btn {
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:active {
  transform: translateY(4rpx);
}

.btn-secondary {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2rpx solid #667eea;
}

.btn-danger {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  margin: 20rpx;
  overflow: hidden;
}

.card-header {
  padding: 30rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.card-body {
  padding: 40rpx;
}

/* 表单样式 */
.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-weight: 600;
  color: #333;
  font-size: 28rpx;
}

.required {
  color: #e74c3c;
}

.form-input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 4rpx solid #e1e8ed;
  border-radius: 16rpx;
  font-size: 32rpx;
  background: white;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
}

.form-select {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 4rpx solid #e1e8ed;
  border-radius: 16rpx;
  font-size: 32rpx;
  background: white;
  box-sizing: border-box;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.checkbox-item {
  padding: 16rpx 32rpx;
  border: 4rpx solid #e1e8ed;
  border-radius: 40rpx;
  background: white;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.checkbox-item.selected {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* 进度条 */
.progress-bar {
  height: 8rpx;
  background: #e1e8ed;
  border-radius: 4rpx;
  margin: 20rpx 0;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: #666;
}

.loading-text {
  margin-left: 20rpx;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #666;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.5;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-10 { margin-top: 20rpx; }
.mt-20 { margin-top: 40rpx; }
.mt-30 { margin-top: 60rpx; }

.mb-10 { margin-bottom: 20rpx; }
.mb-20 { margin-bottom: 40rpx; }
.mb-30 { margin-bottom: 60rpx; }

.p-10 { padding: 20rpx; }
.p-20 { padding: 40rpx; }
.p-30 { padding: 60rpx; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }

.w-full { width: 100%; }
.h-full { height: 100%; }