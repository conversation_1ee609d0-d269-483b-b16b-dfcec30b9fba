# 志愿AI规划师 - 开发指南

## 项目概述

志愿AI规划师是一个基于微信小程序的高考志愿填报指导平台，通过AI技术为学生提供个性化的院校推荐和职业规划服务。

## 开发环境搭建

### 必需工具

1. **微信开发者工具**
   - 下载地址：https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
   - 版本要求：最新稳定版

2. **Node.js**
   - 版本要求：14.x 或更高
   - 用于云函数开发和包管理

3. **代码编辑器**
   - 推荐：VS Code
   - 安装小程序相关插件

### 项目初始化

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd dev_wx_baokao
   ```

2. **配置小程序**
   - 在微信开发者工具中导入项目
   - 修改 `project.config.json` 中的 `appid`
   - 配置云开发环境

3. **安装依赖**
   ```bash
   # 如果有云函数依赖
   cd cloudfunctions/[function-name]
   npm install
   ```

## 项目结构详解

### 目录结构

```
miniprogram/
├── pages/                  # 页面目录
│   ├── home/              # 首页
│   ├── login/             # 登录页
│   ├── form/              # 信息收集页
│   ├── results/           # 推荐结果页
│   ├── career/            # 职业选择页
│   ├── simulation/        # AI人生模拟页
│   └── profile/           # 个人中心页
├── components/            # 组件目录
│   └── loading/           # 加载组件
├── utils/                 # 工具类目录
│   ├── api.js            # API服务
│   ├── util.js           # 通用工具函数
│   ├── config.js         # 配置文件
│   ├── constants.js      # 常量定义
│   ├── event.js          # 事件管理
│   ├── validator.js      # 数据验证
│   └── error-handler.js  # 错误处理
├── images/               # 图片资源
├── app.js               # 应用入口
├── app.json             # 应用配置
└── app.wxss             # 全局样式
```

### 核心文件说明

#### app.js
应用的入口文件，包含：
- 全局数据管理
- API请求封装
- 用户状态管理
- 错误处理
- 事件管理

#### app.json
应用配置文件，定义：
- 页面路由
- tabBar配置
- 窗口样式
- 权限设置

#### app.wxss
全局样式文件，包含：
- 通用样式类
- 组件样式
- 响应式布局
- 主题色彩

## 开发规范

### 代码规范

1. **命名规范**
   - 文件名：小写字母，用连字符分隔（kebab-case）
   - 变量名：驼峰命名法（camelCase）
   - 常量名：大写字母，用下划线分隔（UPPER_SNAKE_CASE）
   - 类名：帕斯卡命名法（PascalCase）

2. **注释规范**
   ```javascript
   /**
    * 函数描述
    * @param {string} param1 参数1描述
    * @param {number} param2 参数2描述
    * @returns {boolean} 返回值描述
    */
   function exampleFunction(param1, param2) {
     // 实现逻辑
   }
   ```

3. **代码格式**
   - 使用2个空格缩进
   - 行末不留空格
   - 文件末尾保留一个空行

### 页面开发规范

1. **页面结构**
   ```
   pages/[page-name]/
   ├── [page-name].js    # 页面逻辑
   ├── [page-name].wxml  # 页面结构
   ├── [page-name].wxss  # 页面样式
   └── [page-name].json  # 页面配置
   ```

2. **页面生命周期**
   ```javascript
   Page({
     data: {
       // 页面数据
     },
     
     onLoad(options) {
       // 页面加载
     },
     
     onShow() {
       // 页面显示
     },
     
     onReady() {
       // 页面初次渲染完成
     },
     
     onHide() {
       // 页面隐藏
     },
     
     onUnload() {
       // 页面卸载
     }
   });
   ```

3. **事件处理**
   ```javascript
   // 事件处理函数以 on 或 handle 开头
   onButtonTap() {
     // 处理按钮点击
   },
   
   handleInputChange(e) {
     // 处理输入变化
   }
   ```

### 组件开发规范

1. **组件结构**
   ```
   components/[component-name]/
   ├── [component-name].js    # 组件逻辑
   ├── [component-name].wxml  # 组件结构
   ├── [component-name].wxss  # 组件样式
   └── [component-name].json  # 组件配置
   ```

2. **组件定义**
   ```javascript
   Component({
     properties: {
       // 组件属性
     },
     
     data: {
       // 组件数据
     },
     
     methods: {
       // 组件方法
     },
     
     lifetimes: {
       // 组件生命周期
     }
   });
   ```

## API 集成

### API 服务使用

```javascript
const app = getApp();

// 发起API请求
try {
  const result = await app.request({
    url: '/api/endpoint',
    method: 'POST',
    data: {
      // 请求数据
    }
  });
  
  // 处理成功响应
  console.log(result.data);
} catch (error) {
  // 错误已由全局错误处理器处理
  console.error('请求失败:', error);
}
```

### 错误处理

```javascript
const { errorHandler } = require('../../utils/error-handler');

// 手动处理错误
try {
  // 可能出错的代码
} catch (error) {
  errorHandler.handleApiError(error, {
    showToast: true,
    logError: true
  });
}
```

### 数据验证

```javascript
const { createValidator, CommonRules } = require('../../utils/validator');

// 创建验证器
const validator = createValidator()
  .addRule('phone', [CommonRules.required, CommonRules.phone], '请输入正确的手机号')
  .addRule('score', [CommonRules.required, CommonRules.score], '请输入有效的分数');

// 验证数据
const result = validator.validate(formData);
if (!result.isValid) {
  app.showError(result.firstError);
  return;
}
```

## 状态管理

### 全局状态

```javascript
const app = getApp();

// 获取用户信息
const userInfo = app.getUserInfo();

// 检查登录状态
if (app.isLoggedIn()) {
  // 已登录逻辑
}

// 更新用户信息
app.updateUserInfo({
  nickname: '新昵称'
});
```

### 事件通信

```javascript
const { eventManager, Events } = require('../../utils/event');

// 监听事件
eventManager.on(Events.USER_LOGIN, (data) => {
  console.log('用户登录:', data);
});

// 触发事件
eventManager.emit(Events.USER_LOGIN, {
  userInfo: userInfo
});
```

## 调试技巧

### 控制台调试

1. **使用 console.log**
   ```javascript
   console.log('调试信息:', data);
   console.error('错误信息:', error);
   console.warn('警告信息:', warning);
   ```

2. **使用 debugger**
   ```javascript
   function debugFunction() {
     debugger; // 断点
     // 代码逻辑
   }
   ```

### 网络调试

1. **查看网络请求**
   - 在开发者工具的 Network 面板查看请求
   - 检查请求参数和响应数据

2. **模拟网络状态**
   - 在开发者工具中模拟弱网环境
   - 测试网络异常处理

### 真机调试

1. **开启真机调试**
   - 在开发者工具中点击"真机调试"
   - 扫码在手机上调试

2. **查看真机日志**
   - 使用 vConsole 查看手机上的日志
   - 检查真机特有的问题

## 性能优化

### 代码优化

1. **避免频繁的 setData**
   ```javascript
   // 不好的做法
   this.setData({ a: 1 });
   this.setData({ b: 2 });
   
   // 好的做法
   this.setData({
     a: 1,
     b: 2
   });
   ```

2. **使用防抖和节流**
   ```javascript
   const { debounce } = require('../../utils/util');
   
   // 防抖处理搜索
   const debouncedSearch = debounce(this.search, 300);
   ```

### 资源优化

1. **图片优化**
   - 使用 WebP 格式
   - 压缩图片大小
   - 使用适当的分辨率

2. **代码分包**
   - 使用分包加载
   - 按需加载页面

## 测试指南

### 功能测试

1. **页面功能测试**
   - 测试所有交互功能
   - 验证数据流转
   - 检查边界情况

2. **兼容性测试**
   - 测试不同机型
   - 测试不同系统版本
   - 测试不同网络环境

### 性能测试

1. **加载性能**
   - 测试页面加载时间
   - 检查内存使用情况
   - 监控网络请求

2. **用户体验**
   - 测试操作流畅度
   - 检查响应时间
   - 验证错误处理

## 部署发布

### 预发布检查

1. **代码检查**
   - 移除调试代码
   - 检查敏感信息
   - 验证配置文件

2. **功能验证**
   - 完整功能测试
   - 性能测试
   - 兼容性测试

### 发布流程

1. **上传代码**
   - 在开发者工具中点击"上传"
   - 填写版本号和更新说明

2. **提交审核**
   - 在微信公众平台提交审核
   - 等待审核结果

3. **发布上线**
   - 审核通过后发布
   - 监控线上运行状态

## 常见问题

### 开发问题

1. **页面不显示**
   - 检查页面路径配置
   - 验证页面文件完整性

2. **API 请求失败**
   - 检查网络配置
   - 验证请求参数
   - 查看错误日志

3. **样式不生效**
   - 检查样式文件路径
   - 验证选择器优先级
   - 确认样式语法正确

### 性能问题

1. **页面加载慢**
   - 优化图片资源
   - 减少网络请求
   - 使用缓存机制

2. **内存占用高**
   - 检查内存泄漏
   - 优化数据结构
   - 及时清理资源

## 技术支持

如果在开发过程中遇到问题，可以通过以下方式获取帮助：

1. **查看文档**
   - 微信小程序官方文档
   - 项目 README 文件
   - API 接口文档

2. **社区支持**
   - 微信开发者社区
   - GitHub Issues
   - 技术论坛

3. **联系团队**
   - 邮箱：<EMAIL>
   - 微信群：[开发者群]
