# 志愿AI规划师 - 微信小程序

一个基于AI的高考志愿填报指导平台，帮助学生科学规划人生道路。

## 项目简介

志愿AI规划师是一个专业的高考志愿填报指导平台，通过AI技术为学生提供：

- 🎯 **精准推荐**：基于分数和兴趣的个性化院校推荐
- 💡 **详细解释**：每个推荐都有专业理由，让你心里有底
- 🚀 **职业规划**：从专业选择到职业发展的全链条规划
- 🔮 **人生模拟**：AI预测你的职业发展轨迹和人生路径

## 技术栈

- **前端**：微信小程序原生开发
- **后端**：Node.js + Express + TypeScript
- **数据库**：MySQL 8.0 + Redis
- **AI服务**：OpenAI API / Claude API
- **支付**：微信支付
- **云服务**：微信云开发

## 项目结构

```
├── miniprogram/                 # 小程序前端代码
│   ├── pages/                   # 页面文件
│   │   ├── home/               # 首页
│   │   ├── login/              # 登录页
│   │   ├── form/               # 信息收集页
│   │   ├── results/            # 推荐结果页
│   │   ├── career/             # 职业选择页
│   │   ├── simulation/         # AI人生模拟页
│   │   └── profile/            # 个人中心页
│   ├── components/             # 组件
│   │   └── loading/            # 加载组件
│   ├── utils/                  # 工具类
│   │   ├── api.js              # API服务
│   │   └── util.js             # 工具函数
│   ├── images/                 # 图片资源
│   ├── app.js                  # 小程序入口文件
│   ├── app.json                # 小程序配置文件
│   └── app.wxss                # 全局样式文件
├── cloudfunctions/             # 云函数
├── API端口文档.md              # API接口文档
├── 产品原型.md                 # 产品原型设计
└── README.md                   # 项目说明文档
```

## 功能特性

### 核心功能

1. **用户认证**
   - 手机号验证码登录
   - 微信快速登录
   - 邀请码机制

2. **信息收集**
   - 分步骤表单设计
   - 智能验证和提示
   - 草稿保存功能

3. **AI推荐**
   - 基于分数的精准匹配
   - 冲稳保策略分析
   - 详细推荐理由

4. **职业规划**
   - 专业对应职业展示
   - 匹配度智能分析
   - 发展路径规划

5. **人生模拟**
   - 15年发展轨迹预测
   - 多种备选路径
   - 风险因素分析

6. **支付系统**
   - 微信支付集成
   - 订单管理
   - 内容解锁机制

### 技术特性

- **响应式设计**：适配不同屏幕尺寸
- **组件化开发**：可复用的UI组件
- **状态管理**：全局数据管理
- **错误处理**：完善的错误处理机制
- **性能优化**：懒加载、防抖节流等优化
- **安全保障**：数据加密、权限控制

## 快速开始

### 环境要求

- 微信开发者工具
- Node.js 14+
- MySQL 8.0+
- Redis

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd dev_wx_baokao
   ```

2. **配置小程序**
   - 在微信开发者工具中导入项目
   - 配置 `project.config.json` 中的 appid
   - 配置 `miniprogram/app.js` 中的 API 地址

3. **配置云开发**
   - 开通微信云开发
   - 配置云环境 ID
   - 部署云函数

4. **启动项目**
   - 在微信开发者工具中编译运行
   - 真机调试测试功能

## API 接口

详细的API接口文档请参考 [API端口文档.md](./API端口文档.md)

### 主要接口

- `POST /auth/login` - 用户登录
- `POST /student/profile` - 提交学生档案
- `POST /recommendation/generate` - 生成推荐
- `POST /career/match-analysis` - 职业匹配分析
- `POST /simulation/generate` - 生成人生模拟
- `POST /payment/create-order` - 创建订单

## 页面说明

### 首页 (home)
- 产品介绍和功能展示
- 用户状态检查
- 快速入口

### 登录页 (login)
- 手机号验证码登录
- 微信快速登录
- 邀请码输入

### 信息收集页 (form)
- 三步骤表单设计
- 实时验证和提示
- 进度条显示

### 推荐结果页 (results)
- 推荐列表展示
- 分类筛选功能
- 详细分析解锁

### 职业选择页 (career)
- 职业列表展示
- 匹配度分析
- 职业详情查看

### AI人生模拟页 (simulation)
- 时间轴展示
- 发展轨迹预测
- 风险因素分析

### 个人中心页 (profile)
- 用户信息管理
- 功能菜单
- 设置选项

## 开发规范

### 代码规范

- 使用 ES6+ 语法
- 遵循微信小程序开发规范
- 组件化开发
- 注释完整

### 命名规范

- 文件名：小写字母，用连字符分隔
- 变量名：驼峰命名法
- 常量名：大写字母，用下划线分隔
- 类名：帕斯卡命名法

### 目录规范

- 页面文件放在 `pages` 目录下
- 组件文件放在 `components` 目录下
- 工具类放在 `utils` 目录下
- 图片资源放在 `images` 目录下

## 部署说明

### 小程序发布

1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在微信公众平台提交审核
4. 审核通过后发布

### 后端部署

1. 配置生产环境变量
2. 部署到云服务器
3. 配置域名和SSL证书
4. 设置监控和日志

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系我们

- 项目地址：[GitHub仓库地址]
- 问题反馈：[Issues页面]
- 邮箱：<EMAIL>

## 更新日志

### v1.0.0 (2025-01-09)
- 初始版本发布
- 完成核心功能开发
- 支持用户登录、信息收集、AI推荐、职业规划、人生模拟等功能

