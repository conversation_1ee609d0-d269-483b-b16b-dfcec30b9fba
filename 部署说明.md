# 志愿AI规划师 - 部署说明

## 部署概述

本文档详细说明了志愿AI规划师小程序的部署流程，包括开发环境配置、生产环境部署和运维监控等内容。

## 环境要求

### 开发环境

- **微信开发者工具**：最新稳定版
- **Node.js**：14.x 或更高版本
- **npm/yarn**：最新版本
- **Git**：版本控制工具

### 生产环境

- **微信小程序平台**：已注册的小程序账号
- **云开发环境**：微信云开发或其他云服务
- **后端服务器**：支持 Node.js 的云服务器
- **数据库**：MySQL 8.0+ / MongoDB 4.0+
- **缓存服务**：Redis 6.0+

## 配置文件说明

### 小程序配置

#### app.json
```json
{
  "pages": [
    "pages/home/<USER>",
    "pages/login/login",
    "pages/form/form",
    "pages/results/results",
    "pages/career/career",
    "pages/simulation/simulation",
    "pages/profile/profile"
  ],
  "window": {
    "backgroundColor": "#f7f8fa",
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#667eea",
    "navigationBarTitleText": "志愿AI规划师",
    "navigationBarTextStyle": "white"
  },
  "tabBar": {
    "color": "#666",
    "selectedColor": "#667eea",
    "backgroundColor": "#ffffff",
    "list": [...]
  }
}
```

#### project.config.json
```json
{
  "appid": "你的小程序AppID",
  "projectname": "zhiyuan-ai-planner",
  "miniprogramRoot": "miniprogram/",
  "cloudfunctionRoot": "cloudfunctions/",
  "setting": {
    "urlCheck": true,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "minified": true
  }
}
```

### 环境配置

#### 开发环境 (utils/config.js)
```javascript
const config = {
  api: {
    baseUrl: 'https://dev-api.zhiyuan-ai.com/v1',
    timeout: 10000
  },
  payment: {
    detailedAnalysisPrice: 0.01, // 测试价格
    simulationReportPrice: 0.01,
    premiumPackagePrice: 0.01
  }
};
```

#### 生产环境 (utils/config.js)
```javascript
const config = {
  api: {
    baseUrl: 'https://api.zhiyuan-ai.com/v1',
    timeout: 10000
  },
  payment: {
    detailedAnalysisPrice: 19.9,
    simulationReportPrice: 199,
    premiumPackagePrice: 499
  }
};
```

## 部署流程

### 1. 代码准备

#### 克隆代码
```bash
git clone https://github.com/your-org/zhiyuan-ai-planner.git
cd zhiyuan-ai-planner
```

#### 安装依赖
```bash
# 如果有云函数依赖
cd cloudfunctions/login
npm install

cd ../recommendation
npm install

cd ../payment
npm install
```

#### 配置环境
```bash
# 复制配置文件
cp miniprogram/utils/config.example.js miniprogram/utils/config.js

# 编辑配置文件
vim miniprogram/utils/config.js
```

### 2. 小程序配置

#### 设置 AppID
1. 在微信公众平台获取小程序 AppID
2. 修改 `project.config.json` 中的 `appid` 字段
3. 在微信开发者工具中导入项目

#### 配置域名白名单
在微信公众平台 -> 开发 -> 开发设置中配置：

**request 合法域名：**
- https://api.zhiyuan-ai.com
- https://your-backend-domain.com

**uploadFile 合法域名：**
- https://api.zhiyuan-ai.com

**downloadFile 合法域名：**
- https://api.zhiyuan-ai.com

#### 配置业务域名
如果需要使用 web-view 组件：
- https://h5.zhiyuan-ai.com

### 3. 云开发配置

#### 开通云开发
1. 在微信开发者工具中开通云开发
2. 创建云开发环境（建议创建两个环境：dev 和 prod）
3. 记录环境 ID

#### 配置云函数
```bash
# 部署云函数
cd cloudfunctions/login
npm install
# 在开发者工具中右键部署

cd ../recommendation
npm install
# 在开发者工具中右键部署

cd ../payment
npm install
# 在开发者工具中右键部署
```

#### 配置数据库
1. 在云开发控制台创建数据库集合
2. 设置数据库权限
3. 导入初始数据（如果有）

#### 配置存储
1. 设置存储权限
2. 上传静态资源（如默认头像等）

### 4. 后端服务部署

#### 服务器配置
```bash
# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PM2
npm install -g pm2

# 安装 Nginx
sudo apt-get install nginx
```

#### 部署后端代码
```bash
# 克隆后端代码
git clone https://github.com/your-org/zhiyuan-ai-backend.git
cd zhiyuan-ai-backend

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
vim .env

# 启动服务
pm2 start ecosystem.config.js --env production
```

#### 配置 Nginx
```nginx
server {
    listen 80;
    server_name api.zhiyuan-ai.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 配置 SSL 证书
```bash
# 使用 Let's Encrypt
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d api.zhiyuan-ai.com
```

### 5. 数据库配置

#### MySQL 配置
```sql
-- 创建数据库
CREATE DATABASE zhiyuan_ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'zhiyuan_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON zhiyuan_ai.* TO 'zhiyuan_user'@'localhost';
FLUSH PRIVILEGES;

-- 导入数据结构
mysql -u zhiyuan_user -p zhiyuan_ai < database/schema.sql

-- 导入初始数据
mysql -u zhiyuan_user -p zhiyuan_ai < database/data.sql
```

#### Redis 配置
```bash
# 安装 Redis
sudo apt-get install redis-server

# 配置 Redis
sudo vim /etc/redis/redis.conf

# 设置密码
requirepass your_redis_password

# 重启 Redis
sudo systemctl restart redis-server
```

### 6. 第三方服务配置

#### 微信支付配置
1. 在微信商户平台获取商户号和密钥
2. 配置支付回调地址
3. 上传 API 证书

#### AI 服务配置
1. 申请 OpenAI API Key 或其他 AI 服务
2. 配置 API 密钥和端点
3. 设置请求限制和重试机制

## 测试部署

### 1. 功能测试

#### 基础功能测试
- [ ] 用户注册/登录
- [ ] 信息收集表单
- [ ] AI 推荐生成
- [ ] 职业选择
- [ ] 人生模拟
- [ ] 支付功能

#### 接口测试
```bash
# 测试 API 接口
curl -X POST https://api.zhiyuan-ai.com/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000","verification_code":"123456"}'
```

#### 性能测试
```bash
# 使用 ab 进行压力测试
ab -n 1000 -c 10 https://api.zhiyuan-ai.com/v1/health
```

### 2. 小程序测试

#### 开发版测试
1. 在开发者工具中预览
2. 扫码在手机上测试
3. 检查所有功能是否正常

#### 体验版测试
1. 上传体验版
2. 邀请测试用户
3. 收集测试反馈

## 生产部署

### 1. 代码上传

#### 小程序代码上传
1. 在微信开发者工具中点击"上传"
2. 填写版本号（如：1.0.0）
3. 填写项目备注
4. 确认上传

#### 版本管理
```bash
# 创建发布分支
git checkout -b release/v1.0.0

# 更新版本号
npm version 1.0.0

# 推送到远程
git push origin release/v1.0.0

# 创建 Tag
git tag v1.0.0
git push origin v1.0.0
```

### 2. 审核提交

#### 提交审核
1. 登录微信公众平台
2. 进入版本管理
3. 选择要提交的版本
4. 填写审核信息
5. 提交审核

#### 审核信息填写
- **功能页面**：选择主要功能页面
- **测试账号**：提供测试账号和密码
- **补充说明**：详细说明功能和使用方法

### 3. 发布上线

#### 审核通过后
1. 在微信公众平台点击"发布"
2. 确认发布版本
3. 设置发布时间（可选）

#### 灰度发布（可选）
1. 设置灰度比例
2. 监控灰度用户反馈
3. 逐步扩大发布范围

## 监控运维

### 1. 日志监控

#### 应用日志
```javascript
// 在代码中添加日志
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

#### 错误监控
```javascript
// 集成错误监控服务
const Sentry = require('@sentry/node');

Sentry.init({
  dsn: 'your-sentry-dsn'
});
```

### 2. 性能监控

#### 服务器监控
```bash
# 安装监控工具
npm install -g pm2
pm2 install pm2-server-monit

# 查看服务状态
pm2 status
pm2 logs
```

#### 数据库监控
```sql
-- 监控数据库性能
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Queries';
```

### 3. 备份策略

#### 数据库备份
```bash
#!/bin/bash
# 数据库备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u zhiyuan_user -p zhiyuan_ai > backup_$DATE.sql
```

#### 代码备份
```bash
# 定期推送到远程仓库
git push origin main

# 创建备份分支
git checkout -b backup/$(date +%Y%m%d)
git push origin backup/$(date +%Y%m%d)
```

## 故障处理

### 常见问题

#### 1. 小程序无法访问
- 检查域名配置
- 验证 SSL 证书
- 查看服务器状态

#### 2. API 请求失败
- 检查网络连接
- 验证接口地址
- 查看服务器日志

#### 3. 支付功能异常
- 检查商户配置
- 验证证书有效性
- 查看支付日志

### 应急处理

#### 服务降级
```javascript
// 在关键接口添加降级逻辑
if (systemOverload) {
  return fallbackResponse;
}
```

#### 快速回滚
```bash
# 回滚到上一个版本
pm2 stop all
git checkout v1.0.0
npm install
pm2 start ecosystem.config.js
```

## 安全配置

### 1. 服务器安全

#### 防火墙配置
```bash
# 配置 UFW 防火墙
sudo ufw enable
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
```

#### SSH 安全
```bash
# 禁用 root 登录
sudo vim /etc/ssh/sshd_config
# PermitRootLogin no

# 重启 SSH 服务
sudo systemctl restart ssh
```

### 2. 应用安全

#### 环境变量
```bash
# 设置敏感信息为环境变量
export DB_PASSWORD="your_db_password"
export JWT_SECRET="your_jwt_secret"
export WECHAT_APP_SECRET="your_wechat_secret"
```

#### API 安全
```javascript
// 添加请求限制
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

app.use('/api/', limiter);
```

## 联系支持

如果在部署过程中遇到问题，请联系技术支持：

- **邮箱**：<EMAIL>
- **电话**：400-xxx-xxxx
- **微信群**：[运维支持群]
- **文档**：https://docs.zhiyuan-ai.com
